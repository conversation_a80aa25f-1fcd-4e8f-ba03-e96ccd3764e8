{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/notebook/browser/contrib/cellStatusBar/contributedStatusBarItemController.ts\n@@\n\n@@ private _updateEverything(): void {\n\t\tconst visibleCellHandles = new Set(this._observer.visibleCells.map(item => item.handle));\n\t\tconst currentCellHandles = Array.from(this._visibleCells.keys());\n+// Inserted line 38\n\t\tconst removedCells = currentCellHandles.filter(handle => !visibleCellHandles.has(handle));\n\t\tconst itemsToUpdate = currentCellHandles.filter(handle => visibleCellHandles.has(handle));\n\n\n@@ }): void {\n\t\t}\n\n+// Inserted line 54\n\t\tfor (const newCell of e.added) {\n\t\t\tconst helper = new CellStatusBarHelper(vm, newCell, this._notebookCellStatusBarService);\n\t\t\tthis._visibleCells.set(newCell.handle, helper);\n\n@@\n\t\tconst newIds = this._notebookViewModel.deltaCellStatusBarItems(this._currentItemIds, [{ handle: this._cell.handle, items }]);\n\n-\t\tthis._currentItemLists.forEach(itemList => itemList.dispose && itemList.dispose());\n+// Replaced line 126\n\t\tthis._currentItemLists = itemLists;\n\t\tthis._currentItemIds = newIds;\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Throttler } from '../../../../../../base/common/async.js';\nimport { CancellationTokenSource } from '../../../../../../base/common/cancellation.js';\nimport { Disposable, toDisposable } from '../../../../../../base/common/lifecycle.js';\nimport { NotebookVisibleCellObserver } from './notebookVisibleCellObserver.js';\nimport { ICellViewModel, INotebookEditor, INotebookEditorContribution, INotebookViewModel } from '../../notebookBrowser.js';\nimport { registerNotebookContribution } from '../../notebookEditorExtensions.js';\nimport { INotebookCellStatusBarService } from '../../../common/notebookCellStatusBarService.js';\nimport { INotebookCellStatusBarItemList } from '../../../common/notebookCommon.js';\n\nexport class ContributedStatusBarItemController extends Disposable implements INotebookEditorContribution {\n\tstatic id: string = 'workbench.notebook.statusBar.contributed';\n\n\tprivate readonly _visibleCells = new Map<number, CellStatusBarHelper>();\n\n\tprivate readonly _observer: NotebookVisibleCellObserver;\n\n\tconstructor(\n\t\tprivate readonly _notebookEditor: INotebookEditor,\n\t\t@INotebookCellStatusBarService private readonly _notebookCellStatusBarService: INotebookCellStatusBarService\n\t) {\n\t\tsuper();\n\t\tthis._observer = this._register(new NotebookVisibleCellObserver(this._notebookEditor));\n\t\tthis._register(this._observer.onDidChangeVisibleCells(this._updateVisibleCells, this));\n\n\t\tthis._updateEverything();\n\t\tthis._register(this._notebookCellStatusBarService.onDidChangeProviders(this._updateEverything, this));\n\t\tthis._register(this._notebookCellStatusBarService.onDidChangeItems(this._updateEverything, this));\n\t}\n\n\tprivate _updateEverything(): void {\n\t\tconst newCells = this._observer.visibleCells.filter(cell => !this._visibleCells.has(cell.handle));\n\t\tconst visibleCellHandles = new Set(this._observer.visibleCells.map(item => item.handle));\n\t\tconst currentCellHandles = Array.from(this._visibleCells.keys());\n\t\tconst removedCells = currentCellHandles.filter(handle => !visibleCellHandles.has(handle));\n\t\tconst itemsToUpdate = currentCellHandles.filter(handle => visibleCellHandles.has(handle));\n\n\t\tthis._updateVisibleCells({ added: newCells, removed: removedCells.map(handle => ({ handle })) });\n\t\titemsToUpdate.forEach(handle => this._visibleCells.get(handle)?.update());\n\t}\n\n\tprivate _updateVisibleCells(e: {\n\t\tadded: ICellViewModel[];\n\t\tremoved: { handle: number }[];\n\t}): void {\n\t\tconst vm = this._notebookEditor.getViewModel();\n\t\tif (!vm) {\n\t\t\treturn;\n\t\t}\n\n\t\tfor (const newCell of e.added) {\n\t\t\tconst helper = new CellStatusBarHelper(vm, newCell, this._notebookCellStatusBarService);\n\t\t\tthis._visibleCells.set(newCell.handle, helper);\n\t\t}\n\n\t\tfor (const oldCell of e.removed) {\n\t\t\tthis._visibleCells.get(oldCell.handle)?.dispose();\n\t\t\tthis._visibleCells.delete(oldCell.handle);\n\t\t}\n\t}\n\n\toverride dispose(): void {\n\t\tsuper.dispose();\n\n\t\tthis._visibleCells.forEach(cell => cell.dispose());\n\t\tthis._visibleCells.clear();\n\t}\n}\n\nclass CellStatusBarHelper extends Disposable {\n\tprivate _currentItemIds: string[] = [];\n\tprivate _currentItemLists: INotebookCellStatusBarItemList[] = [];\n\n\tprivate _activeToken: CancellationTokenSource | undefined;\n\tprivate _isDisposed = false;\n\n\tprivate readonly _updateThrottler = this._register(new Throttler());\n\n\tconstructor(\n\t\tprivate readonly _notebookViewModel: INotebookViewModel,\n\t\tprivate readonly _cell: ICellViewModel,\n\t\tprivate readonly _notebookCellStatusBarService: INotebookCellStatusBarService\n\t) {\n\t\tsuper();\n\n\t\tthis._register(toDisposable(() => this._activeToken?.dispose(true)));\n\t\tthis._updateSoon();\n\t\tthis._register(this._cell.model.onDidChangeContent(() => this._updateSoon()));\n\t\tthis._register(this._cell.model.onDidChangeLanguage(() => this._updateSoon()));\n\t\tthis._register(this._cell.model.onDidChangeMetadata(() => this._updateSoon()));\n\t\tthis._register(this._cell.model.onDidChangeInternalMetadata(() => this._updateSoon()));\n\t\tthis._register(this._cell.model.onDidChangeOutputs(() => this._updateSoon()));\n\t}\n\n\tpublic update(): void {\n\t\tthis._updateSoon();\n\t}\n\tprivate _updateSoon(): void {\n\t\t// Wait a tick to make sure that the event is fired to the EH before triggering status bar providers\n\t\tsetTimeout(() => {\n\t\t\tif (!this._isDisposed) {\n\t\t\t\tthis._updateThrottler.queue(() => this._update());\n\t\t\t}\n\t\t}, 0);\n\t}\n\n\tprivate async _update() {\n\t\tconst cellIndex = this._notebookViewModel.getCellIndex(this._cell);\n\t\tconst docUri = this._notebookViewModel.notebookDocument.uri;\n\t\tconst viewType = this._notebookViewModel.notebookDocument.viewType;\n\n\t\tthis._activeToken?.dispose(true);\n\t\tconst tokenSource = this._activeToken = new CancellationTokenSource();\n\t\tconst itemLists = await this._notebookCellStatusBarService.getStatusBarItemsForCell(docUri, cellIndex, viewType, tokenSource.token);\n\t\tif (tokenSource.token.isCancellationRequested) {\n\t\t\titemLists.forEach(itemList => itemList.dispose && itemList.dispose());\n\t\t\treturn;\n\t\t}\n\n\t\tconst items = itemLists.map(itemList => itemList.items).flat();\n\t\tconst newIds = this._notebookViewModel.deltaCellStatusBarItems(this._currentItemIds, [{ handle: this._cell.handle, items }]);\n\n\t\tthis._currentItemLists.forEach(itemList => itemList.dispose && itemList.dispose());\n\t\tthis._currentItemLists = itemLists;\n\t\tthis._currentItemIds = newIds;\n\t}\n\n\toverride dispose() {\n\t\tsuper.dispose();\n\t\tthis._isDisposed = true;\n\t\tthis._activeToken?.dispose(true);\n\n\t\tthis._notebookViewModel.deltaCellStatusBarItems(this._currentItemIds, [{ handle: this._cell.handle, items: [] }]);\n\t\tthis._currentItemLists.forEach(itemList => itemList.dispose && itemList.dispose());\n\t}\n}\n\nregisterNotebookContribution(ContributedStatusBarItemController.id, ContributedStatusBarItemController);\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Throttler } from '../../../../../../base/common/async.js';\nimport { CancellationTokenSource } from '../../../../../../base/common/cancellation.js';\nimport { Disposable, toDisposable } from '../../../../../../base/common/lifecycle.js';\nimport { NotebookVisibleCellObserver } from './notebookVisibleCellObserver.js';\nimport { ICellViewModel, INotebookEditor, INotebookEditorContribution, INotebookViewModel } from '../../notebookBrowser.js';\nimport { registerNotebookContribution } from '../../notebookEditorExtensions.js';\nimport { INotebookCellStatusBarService } from '../../../common/notebookCellStatusBarService.js';\nimport { INotebookCellStatusBarItemList } from '../../../common/notebookCommon.js';\n\nexport class ContributedStatusBarItemController extends Disposable implements INotebookEditorContribution {\n\tstatic id: string = 'workbench.notebook.statusBar.contributed';\n\n\tprivate readonly _visibleCells = new Map<number, CellStatusBarHelper>();\n\n\tprivate readonly _observer: NotebookVisibleCellObserver;\n\n\tconstructor(\n\t\tprivate readonly _notebookEditor: INotebookEditor,\n\t\t@INotebookCellStatusBarService private readonly _notebookCellStatusBarService: INotebookCellStatusBarService\n\t) {\n\t\tsuper();\n\t\tthis._observer = this._register(new NotebookVisibleCellObserver(this._notebookEditor));\n\t\tthis._register(this._observer.onDidChangeVisibleCells(this._updateVisibleCells, this));\n\n\t\tthis._updateEverything();\n\t\tthis._register(this._notebookCellStatusBarService.onDidChangeProviders(this._updateEverything, this));\n\t\tthis._register(this._notebookCellStatusBarService.onDidChangeItems(this._updateEverything, this));\n\t}\n\n\tprivate _updateEverything(): void {\n\t\tconst newCells = this._observer.visibleCells.filter(cell => !this._visibleCells.has(cell.handle));\n\t\tconst visibleCellHandles = new Set(this._observer.visibleCells.map(item => item.handle));\n\t\tconst currentCellHandles = Array.from(this._visibleCells.keys());\n// Inserted line 38\n\t\tconst removedCells = currentCellHandles.filter(handle => !visibleCellHandles.has(handle));\n\t\tconst itemsToUpdate = currentCellHandles.filter(handle => visibleCellHandles.has(handle));\n\n\t\tthis._updateVisibleCells({ added: newCells, removed: removedCells.map(handle => ({ handle })) });\n\t\titemsToUpdate.forEach(handle => this._visibleCells.get(handle)?.update());\n\t}\n\n\tprivate _updateVisibleCells(e: {\n\t\tadded: ICellViewModel[];\n\t\tremoved: { handle: number }[];\n\t}): void {\n\t\tconst vm = this._notebookEditor.getViewModel();\n\t\tif (!vm) {\n\t\t\treturn;\n\t\t}\n\n// Inserted line 54\n\t\tfor (const newCell of e.added) {\n\t\t\tconst helper = new CellStatusBarHelper(vm, newCell, this._notebookCellStatusBarService);\n\t\t\tthis._visibleCells.set(newCell.handle, helper);\n\t\t}\n\n\t\tfor (const oldCell of e.removed) {\n\t\t\tthis._visibleCells.get(oldCell.handle)?.dispose();\n\t\t\tthis._visibleCells.delete(oldCell.handle);\n\t\t}\n\t}\n\n\toverride dispose(): void {\n\t\tsuper.dispose();\n\n\t\tthis._visibleCells.forEach(cell => cell.dispose());\n\t\tthis._visibleCells.clear();\n\t}\n}\n\nclass CellStatusBarHelper extends Disposable {\n\tprivate _currentItemIds: string[] = [];\n\tprivate _currentItemLists: INotebookCellStatusBarItemList[] = [];\n\n\tprivate _activeToken: CancellationTokenSource | undefined;\n\tprivate _isDisposed = false;\n\n\tprivate readonly _updateThrottler = this._register(new Throttler());\n\n\tconstructor(\n\t\tprivate readonly _notebookViewModel: INotebookViewModel,\n\t\tprivate readonly _cell: ICellViewModel,\n\t\tprivate readonly _notebookCellStatusBarService: INotebookCellStatusBarService\n\t) {\n\t\tsuper();\n\n\t\tthis._register(toDisposable(() => this._activeToken?.dispose(true)));\n\t\tthis._updateSoon();\n\t\tthis._register(this._cell.model.onDidChangeContent(() => this._updateSoon()));\n\t\tthis._register(this._cell.model.onDidChangeLanguage(() => this._updateSoon()));\n\t\tthis._register(this._cell.model.onDidChangeMetadata(() => this._updateSoon()));\n\t\tthis._register(this._cell.model.onDidChangeInternalMetadata(() => this._updateSoon()));\n\t\tthis._register(this._cell.model.onDidChangeOutputs(() => this._updateSoon()));\n\t}\n\n\tpublic update(): void {\n\t\tthis._updateSoon();\n\t}\n\tprivate _updateSoon(): void {\n\t\t// Wait a tick to make sure that the event is fired to the EH before triggering status bar providers\n\t\tsetTimeout(() => {\n\t\t\tif (!this._isDisposed) {\n\t\t\t\tthis._updateThrottler.queue(() => this._update());\n\t\t\t}\n\t\t}, 0);\n\t}\n\n\tprivate async _update() {\n\t\tconst cellIndex = this._notebookViewModel.getCellIndex(this._cell);\n\t\tconst docUri = this._notebookViewModel.notebookDocument.uri;\n\t\tconst viewType = this._notebookViewModel.notebookDocument.viewType;\n\n\t\tthis._activeToken?.dispose(true);\n\t\tconst tokenSource = this._activeToken = new CancellationTokenSource();\n\t\tconst itemLists = await this._notebookCellStatusBarService.getStatusBarItemsForCell(docUri, cellIndex, viewType, tokenSource.token);\n\t\tif (tokenSource.token.isCancellationRequested) {\n\t\t\titemLists.forEach(itemList => itemList.dispose && itemList.dispose());\n\t\t\treturn;\n\t\t}\n\n\t\tconst items = itemLists.map(itemList => itemList.items).flat();\n\t\tconst newIds = this._notebookViewModel.deltaCellStatusBarItems(this._currentItemIds, [{ handle: this._cell.handle, items }]);\n\n// Replaced line 126\n\t\tthis._currentItemLists = itemLists;\n\t\tthis._currentItemIds = newIds;\n\t}\n\n\toverride dispose() {\n\t\tsuper.dispose();\n\t\tthis._isDisposed = true;\n\t\tthis._activeToken?.dispose(true);\n\n\t\tthis._notebookViewModel.deltaCellStatusBarItems(this._currentItemIds, [{ handle: this._cell.handle, items: [] }]);\n\t\tthis._currentItemLists.forEach(itemList => itemList.dispose && itemList.dispose());\n\t}\n}\n\nregisterNotebookContribution(ContributedStatusBarItemController.id, ContributedStatusBarItemController);\n", "fpath": "/vs/workbench/contrib/notebook/browser/contrib/cellStatusBar/contributedStatusBarItemController.ts"}