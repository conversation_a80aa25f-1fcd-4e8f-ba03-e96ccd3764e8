{"patch": "*** Begin Patch\n*** Update File: /vs/workbench/contrib/mcp/common/mcpServerRequestHandler.ts\n@@\n\n@@ export interface IMcpServerRequestHandlerOptions extends IMcpClientMethods {\n\trequestLogLevel?: LogLevel;\n}\n-\n/**\n * Request handler for communicating with an MCP server.\n\n@@\n\n\t\t\tconst result: R = await this.sendRequest<T, R>({ method, params }, token);\n-\t\t\tyield getItems(result);\n+// Replaced line 241\n\t\t\tnextCursor = result.nextCursor;\n\t\t} while (nextCursor !== undefined && !token.isCancellationRequested);\n\n@@ switch (request.params?.level) {\n\t\t\tcase 'debug':\n\t\t\t\tthis.logger.debug(contents);\n-\t\t\t\tbreak;\n\t\t\tcase 'info':\n\t\t\tcase 'notice':\n*** End Patch", "original": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { equals } from '../../../../base/common/arrays.js';\nimport { DeferredPromise, IntervalTimer } from '../../../../base/common/async.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { CancellationError } from '../../../../base/common/errors.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { Iterable } from '../../../../base/common/iterator.js';\nimport { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { autorun } from '../../../../base/common/observable.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nimport { canLog, ILogger, log, LogLevel } from '../../../../platform/log/common/log.js';\nimport { IProductService } from '../../../../platform/product/common/productService.js';\nimport { IMcpMessageTransport } from './mcpRegistryTypes.js';\nimport { IMcpClientMethods, McpConnectionState, McpError, MpcResponseError } from './mcpTypes.js';\nimport { MCP } from './modelContextProtocol.js';\n\n/**\n * Maps request IDs to handlers.\n */\ninterface PendingRequest {\n\tpromise: DeferredPromise<MCP.Result>;\n}\n\nexport interface McpRoot {\n\turi: string;\n\tname?: string;\n}\n\nexport interface IMcpServerRequestHandlerOptions extends IMcpClientMethods {\n\t/** MCP message transport */\n\tlaunch: IMcpMessageTransport;\n\t/** Logger instance. */\n\tlogger: ILogger;\n\t/** Log level MCP messages is logged at */\n\trequestLogLevel?: LogLevel;\n}\n\n/**\n * Request handler for communicating with an MCP server.\n *\n * Handles sending requests and receiving responses, with automatic\n * handling of ping requests and typed client request methods.\n */\nexport class McpServerRequestHandler extends Disposable {\n\tprivate _nextRequestId = 1;\n\tprivate readonly _pendingRequests = new Map<MCP.RequestId, PendingRequest>();\n\n\tprivate _hasAnnouncedRoots = false;\n\tprivate _roots: MCP.Root[] = [];\n\n\tpublic set roots(roots: MCP.Root[]) {\n\t\tif (!equals(this._roots, roots)) {\n\t\t\tthis._roots = roots;\n\t\t\tif (this._hasAnnouncedRoots) {\n\t\t\t\tthis.sendNotification({ method: 'notifications/roots/list_changed' });\n\t\t\t\tthis._hasAnnouncedRoots = false;\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate _serverInit!: MCP.InitializeResult;\n\tpublic get capabilities(): MCP.ServerCapabilities {\n\t\treturn this._serverInit.capabilities;\n\t}\n\n\tpublic get serverInfo(): MCP.Implementation {\n\t\treturn this._serverInit.serverInfo;\n\t}\n\n\t// Event emitters for server notifications\n\tprivate readonly _onDidReceiveCancelledNotification = this._register(new Emitter<MCP.CancelledNotification>());\n\treadonly onDidReceiveCancelledNotification = this._onDidReceiveCancelledNotification.event;\n\n\tprivate readonly _onDidReceiveProgressNotification = this._register(new Emitter<MCP.ProgressNotification>());\n\treadonly onDidReceiveProgressNotification = this._onDidReceiveProgressNotification.event;\n\n\tprivate readonly _onDidChangeResourceList = this._register(new Emitter<void>());\n\treadonly onDidChangeResourceList = this._onDidChangeResourceList.event;\n\n\tprivate readonly _onDidUpdateResource = this._register(new Emitter<MCP.ResourceUpdatedNotification>());\n\treadonly onDidUpdateResource = this._onDidUpdateResource.event;\n\n\tprivate readonly _onDidChangeToolList = this._register(new Emitter<void>());\n\treadonly onDidChangeToolList = this._onDidChangeToolList.event;\n\n\tprivate readonly _onDidChangePromptList = this._register(new Emitter<void>());\n\treadonly onDidChangePromptList = this._onDidChangePromptList.event;\n\n\t/**\n\t * Connects to the MCP server and does the initialization handshake.\n\t * @throws MpcResponseError if the server fails to initialize.\n\t */\n\tpublic static async create(instaService: IInstantiationService, opts: IMcpServerRequestHandlerOptions, token?: CancellationToken) {\n\t\tconst mcp = new McpServerRequestHandler(opts);\n\t\tconst store = new DisposableStore();\n\t\ttry {\n\t\t\tconst timer = store.add(new IntervalTimer());\n\t\t\ttimer.cancelAndSet(() => {\n\t\t\t\topts.logger.info('Waiting for server to respond to `initialize` request...');\n\t\t\t}, 5000);\n\n\t\t\tawait instaService.invokeFunction(async accessor => {\n\t\t\t\tconst productService = accessor.get(IProductService);\n\t\t\t\tconst initialized = await mcp.sendRequest<MCP.InitializeRequest, MCP.InitializeResult>({\n\t\t\t\t\tmethod: 'initialize',\n\t\t\t\t\tparams: {\n\t\t\t\t\t\tprotocolVersion: MCP.LATEST_PROTOCOL_VERSION,\n\t\t\t\t\t\tcapabilities: {\n\t\t\t\t\t\t\troots: { listChanged: true },\n\t\t\t\t\t\t\tsampling: opts.createMessageRequestHandler ? {} : undefined,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tclientInfo: {\n\t\t\t\t\t\t\tname: productService.nameLong,\n\t\t\t\t\t\t\tversion: productService.version,\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}, token);\n\n\t\t\t\tmcp._serverInit = initialized;\n\n\t\t\t\tmcp.sendNotification<MCP.InitializedNotification>({\n\t\t\t\t\tmethod: 'notifications/initialized'\n\t\t\t\t});\n\t\t\t});\n\n\t\t\treturn mcp;\n\t\t} catch (e) {\n\t\t\tmcp.dispose();\n\t\t\tthrow e;\n\t\t} finally {\n\t\t\tstore.dispose();\n\t\t}\n\t}\n\n\tpublic readonly logger: ILogger;\n\tprivate readonly _launch: IMcpMessageTransport;\n\tprivate readonly _requestLogLevel: LogLevel;\n\tprivate readonly _createMessageRequestHandler: IMcpServerRequestHandlerOptions['createMessageRequestHandler'];\n\n\tprotected constructor({\n\t\tlaunch,\n\t\tlogger,\n\t\tcreateMessageRequestHandler,\n\t\trequestLogLevel = LogLevel.Debug,\n\t}: IMcpServerRequestHandlerOptions) {\n\t\tsuper();\n\t\tthis._launch = launch;\n\t\tthis.logger = logger;\n\t\tthis._requestLogLevel = requestLogLevel;\n\t\tthis._createMessageRequestHandler = createMessageRequestHandler;\n\n\t\tthis._register(launch.onDidReceiveMessage(message => this.handleMessage(message)));\n\t\tthis._register(autorun(reader => {\n\t\t\tconst state = launch.state.read(reader).state;\n\t\t\t// the handler will get disposed when the launch stops, but if we're still\n\t\t\t// create()'ing we need to make sure to cancel the initialize request.\n\t\t\tif (state === McpConnectionState.Kind.Error || state === McpConnectionState.Kind.Stopped) {\n\t\t\t\tthis.cancelAllRequests();\n\t\t\t}\n\t\t}));\n\t}\n\n\t/**\n\t * Send a client request to the server and return the response.\n\t *\n\t * @param request The request to send\n\t * @param token Cancellation token\n\t * @param timeoutMs Optional timeout in milliseconds\n\t * @returns A promise that resolves with the response\n\t */\n\tprivate async sendRequest<T extends MCP.ClientRequest, R extends MCP.ServerResult>(\n\t\trequest: Pick<T, 'params' | 'method'>,\n\t\ttoken: CancellationToken = CancellationToken.None\n\t): Promise<R> {\n\t\tif (this._store.isDisposed) {\n\t\t\treturn Promise.reject(new CancellationError());\n\t\t}\n\n\t\tconst id = this._nextRequestId++;\n\n\t\t// Create the full JSON-RPC request\n\t\tconst jsonRpcRequest: MCP.JSONRPCRequest = {\n\t\t\tjsonrpc: MCP.JSONRPC_VERSION,\n\t\t\tid,\n\t\t\t...request\n\t\t};\n\n\t\tconst promise = new DeferredPromise<MCP.ServerResult>();\n\t\t// Store the pending request\n\t\tthis._pendingRequests.set(id, { promise });\n\t\t// Set up cancellation\n\t\tconst cancelListener = token.onCancellationRequested(() => {\n\t\t\tif (!promise.isSettled) {\n\t\t\t\tthis._pendingRequests.delete(id);\n\t\t\t\tthis.sendNotification({ method: 'notifications/cancelled', params: { requestId: id } });\n\t\t\t\tpromise.cancel();\n\t\t\t}\n\t\t\tcancelListener.dispose();\n\t\t});\n\n\t\t// Send the request\n\t\tthis.send(jsonRpcRequest);\n\t\tconst ret = promise.p.finally(() => {\n\t\t\tcancelListener.dispose();\n\t\t\tthis._pendingRequests.delete(id);\n\t\t});\n\n\t\treturn ret as Promise<R>;\n\t}\n\n\tprivate send(mcp: MCP.JSONRPCMessage) {\n\t\tif (canLog(this.logger.getLevel(), this._requestLogLevel)) { // avoid building the string if we don't need to\n\t\t\tlog(this.logger, this._requestLogLevel, `[editor -> server] ${JSON.stringify(mcp)}`);\n\t\t}\n\n\t\tthis._launch.send(mcp);\n\t}\n\n\t/**\n\t * Handles paginated requests by making multiple requests until all items are retrieved.\n\t *\n\t * @param method The method name to call\n\t * @param getItems Function to extract the array of items from a result\n\t * @param initialParams Initial parameters\n\t * @param token Cancellation token\n\t * @returns Promise with all items combined\n\t */\n\tprivate async *sendRequestPaginated<T extends MCP.PaginatedRequest & MCP.ClientRequest, R extends MCP.PaginatedResult, I>(method: T['method'], getItems: (result: R) => I[], initialParams?: Omit<T['params'], 'jsonrpc' | 'id'>, token: CancellationToken = CancellationToken.None): AsyncIterable<I[]> {\n\t\tlet nextCursor: MCP.Cursor | undefined = undefined;\n\n\t\tdo {\n\t\t\tconst params: T['params'] = {\n\t\t\t\t...initialParams,\n\t\t\t\tcursor: nextCursor\n\t\t\t};\n\n\t\t\tconst result: R = await this.sendRequest<T, R>({ method, params }, token);\n\t\t\tyield getItems(result);\n\t\t\tnextCursor = result.nextCursor;\n\t\t} while (nextCursor !== undefined && !token.isCancellationRequested);\n\t}\n\n\tprivate sendNotification<N extends MCP.ClientNotification>(notification: N): void {\n\t\tthis.send({ ...notification, jsonrpc: MCP.JSONRPC_VERSION });\n\t}\n\n\t/**\n\t * Handle incoming messages from the server\n\t */\n\tprivate handleMessage(message: MCP.JSONRPCMessage): void {\n\t\tif (canLog(this.logger.getLevel(), this._requestLogLevel)) { // avoid building the string if we don't need to\n\t\t\tlog(this.logger, this._requestLogLevel, `[server -> editor] ${JSON.stringify(message)}`);\n\t\t}\n\n\t\t// Handle responses to our requests\n\t\tif ('id' in message) {\n\t\t\tif ('result' in message) {\n\t\t\t\tthis.handleResult(message);\n\t\t\t} else if ('error' in message) {\n\t\t\t\tthis.handleError(message);\n\t\t\t}\n\t\t}\n\n\t\t// Handle requests from the server\n\t\tif ('method' in message) {\n\t\t\tif ('id' in message) {\n\t\t\t\tthis.handleServerRequest(message as MCP.JSONRPCRequest & MCP.ServerRequest);\n\t\t\t} else {\n\t\t\t\tthis.handleServerNotification(message as MCP.JSONRPCNotification & MCP.ServerNotification);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Handle successful responses\n\t */\n\tprivate handleResult(response: MCP.JSONRPCResponse): void {\n\t\tconst request = this._pendingRequests.get(response.id);\n\t\tif (request) {\n\t\t\tthis._pendingRequests.delete(response.id);\n\t\t\trequest.promise.complete(response.result);\n\t\t}\n\t}\n\n\t/**\n\t * Handle error responses\n\t */\n\tprivate handleError(response: MCP.JSONRPCError): void {\n\t\tconst request = this._pendingRequests.get(response.id);\n\t\tif (request) {\n\t\t\tthis._pendingRequests.delete(response.id);\n\t\t\trequest.promise.error(new MpcResponseError(response.error.message, response.error.code, response.error.data));\n\t\t}\n\t}\n\n\t/**\n\t * Handle incoming server requests\n\t */\n\tprivate async handleServerRequest(request: MCP.JSONRPCRequest & MCP.ServerRequest): Promise<void> {\n\t\ttry {\n\t\t\tlet response: MCP.Result | undefined;\n\t\t\tif (request.method === 'ping') {\n\t\t\t\tresponse = this.handlePing(request);\n\t\t\t} else if (request.method === 'roots/list') {\n\t\t\t\tresponse = this.handleRootsList(request);\n\t\t\t} else if (request.method === 'sampling/createMessage' && this._createMessageRequestHandler) {\n\t\t\t\tresponse = await this._createMessageRequestHandler(request.params as MCP.CreateMessageRequest['params']);\n\t\t\t} else {\n\t\t\t\tthrow McpError.methodNotFound(request.method);\n\t\t\t}\n\t\t\tthis.respondToRequest(request, response);\n\t\t} catch (e) {\n\t\t\tif (!(e instanceof McpError)) {\n\t\t\t\tthis.logger.error(`Error handling request ${request.method}:`, e);\n\t\t\t\te = McpError.unknown(e);\n\t\t\t}\n\n\t\t\tconst errorResponse: MCP.JSONRPCError = {\n\t\t\t\tjsonrpc: MCP.JSONRPC_VERSION,\n\t\t\t\tid: request.id,\n\t\t\t\terror: {\n\t\t\t\t\tcode: e.code,\n\t\t\t\t\tmessage: e.message,\n\t\t\t\t\tdata: e.data,\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tthis.send(errorResponse);\n\t\t}\n\t}\n\t/**\n\t * Handle incoming server notifications\n\t */\n\tprivate handleServerNotification(request: MCP.JSONRPCNotification & MCP.ServerNotification): void {\n\t\tswitch (request.method) {\n\t\t\tcase 'notifications/message':\n\t\t\t\treturn this.handleLoggingNotification(request);\n\t\t\tcase 'notifications/cancelled':\n\t\t\t\tthis._onDidReceiveCancelledNotification.fire(request);\n\t\t\t\treturn this.handleCancelledNotification(request);\n\t\t\tcase 'notifications/progress':\n\t\t\t\tthis._onDidReceiveProgressNotification.fire(request);\n\t\t\t\treturn;\n\t\t\tcase 'notifications/resources/list_changed':\n\t\t\t\tthis._onDidChangeResourceList.fire();\n\t\t\t\treturn;\n\t\t\tcase 'notifications/resources/updated':\n\t\t\t\tthis._onDidUpdateResource.fire(request);\n\t\t\t\treturn;\n\t\t\tcase 'notifications/tools/list_changed':\n\t\t\t\tthis._onDidChangeToolList.fire();\n\t\t\t\treturn;\n\t\t\tcase 'notifications/prompts/list_changed':\n\t\t\t\tthis._onDidChangePromptList.fire();\n\t\t\t\treturn;\n\t\t}\n\t}\n\n\tprivate handleCancelledNotification(request: MCP.CancelledNotification): void {\n\t\tconst pendingRequest = this._pendingRequests.get(request.params.requestId);\n\t\tif (pendingRequest) {\n\t\t\tthis._pendingRequests.delete(request.params.requestId);\n\t\t\tpendingRequest.promise.cancel();\n\t\t}\n\t}\n\n\tprivate handleLoggingNotification(request: MCP.LoggingMessageNotification): void {\n\t\tlet contents = typeof request.params.data === 'string' ? request.params.data : JSON.stringify(request.params.data);\n\t\tif (request.params.logger) {\n\t\t\tcontents = `${request.params.logger}: ${contents}`;\n\t\t}\n\n\t\tswitch (request.params?.level) {\n\t\t\tcase 'debug':\n\t\t\t\tthis.logger.debug(contents);\n\t\t\t\tbreak;\n\t\t\tcase 'info':\n\t\t\tcase 'notice':\n\t\t\t\tthis.logger.info(contents);\n\t\t\t\tbreak;\n\t\t\tcase 'warning':\n\t\t\t\tthis.logger.warn(contents);\n\t\t\t\tbreak;\n\t\t\tcase 'error':\n\t\t\tcase 'critical':\n\t\t\tcase 'alert':\n\t\t\tcase 'emergency':\n\t\t\t\tthis.logger.error(contents);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tthis.logger.info(contents);\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\t/**\n\t * Send a generic response to a request\n\t */\n\tprivate respondToRequest(request: MCP.JSONRPCRequest, result: MCP.Result): void {\n\t\tconst response: MCP.JSONRPCResponse = {\n\t\t\tjsonrpc: MCP.JSONRPC_VERSION,\n\t\t\tid: request.id,\n\t\t\tresult\n\t\t};\n\t\tthis.send(response);\n\t}\n\n\t/**\n\t * Send a response to a ping request\n\t */\n\tprivate handlePing(_request: MCP.PingRequest): {} {\n\t\treturn {};\n\t}\n\n\t/**\n\t * Send a response to a roots/list request\n\t */\n\tprivate handleRootsList(_request: MCP.ListRootsRequest): MCP.ListRootsResult {\n\t\tthis._hasAnnouncedRoots = true;\n\t\treturn { roots: this._roots };\n\t}\n\n\tprivate cancelAllRequests() {\n\t\tthis._pendingRequests.forEach(pending => pending.promise.cancel());\n\t\tthis._pendingRequests.clear();\n\t}\n\n\tpublic override dispose(): void {\n\t\tthis.cancelAllRequests();\n\t\tsuper.dispose();\n\t}\n\n\t/**\n\t * Send an initialize request\n\t */\n\tinitialize(params: MCP.InitializeRequest['params'], token?: CancellationToken): Promise<MCP.InitializeResult> {\n\t\treturn this.sendRequest<MCP.InitializeRequest, MCP.InitializeResult>({ method: 'initialize', params }, token);\n\t}\n\n\t/**\n\t * List available resources\n\t */\n\tlistResources(params?: MCP.ListResourcesRequest['params'], token?: CancellationToken): Promise<MCP.Resource[]> {\n\t\treturn Iterable.asyncToArrayFlat(this.listResourcesIterable(params, token));\n\t}\n\n\t/**\n\t * List available resources (iterable)\n\t */\n\tlistResourcesIterable(params?: MCP.ListResourcesRequest['params'], token?: CancellationToken): AsyncIterable<MCP.Resource[]> {\n\t\treturn this.sendRequestPaginated<MCP.ListResourcesRequest, MCP.ListResourcesResult, MCP.Resource>('resources/list', result => result.resources, params, token);\n\t}\n\n\t/**\n\t * Read a specific resource\n\t */\n\treadResource(params: MCP.ReadResourceRequest['params'], token?: CancellationToken): Promise<MCP.ReadResourceResult> {\n\t\treturn this.sendRequest<MCP.ReadResourceRequest, MCP.ReadResourceResult>({ method: 'resources/read', params }, token);\n\t}\n\n\t/**\n\t * List available resource templates\n\t */\n\tlistResourceTemplates(params?: MCP.ListResourceTemplatesRequest['params'], token?: CancellationToken): Promise<MCP.ResourceTemplate[]> {\n\t\treturn Iterable.asyncToArrayFlat(this.sendRequestPaginated<MCP.ListResourceTemplatesRequest, MCP.ListResourceTemplatesResult, MCP.ResourceTemplate>('resources/templates/list', result => result.resourceTemplates, params, token));\n\t}\n\n\t/**\n\t * Subscribe to resource updates\n\t */\n\tsubscribe(params: MCP.SubscribeRequest['params'], token?: CancellationToken): Promise<MCP.EmptyResult> {\n\t\treturn this.sendRequest<MCP.SubscribeRequest, MCP.EmptyResult>({ method: 'resources/subscribe', params }, token);\n\t}\n\n\t/**\n\t * Unsubscribe from resource updates\n\t */\n\tunsubscribe(params: MCP.UnsubscribeRequest['params'], token?: CancellationToken): Promise<MCP.EmptyResult> {\n\t\treturn this.sendRequest<MCP.UnsubscribeRequest, MCP.EmptyResult>({ method: 'resources/unsubscribe', params }, token);\n\t}\n\n\t/**\n\t * List available prompts\n\t */\n\tlistPrompts(params?: MCP.ListPromptsRequest['params'], token?: CancellationToken): Promise<MCP.Prompt[]> {\n\t\treturn Iterable.asyncToArrayFlat(this.sendRequestPaginated<MCP.ListPromptsRequest, MCP.ListPromptsResult, MCP.Prompt>('prompts/list', result => result.prompts, params, token));\n\t}\n\n\t/**\n\t * Get a specific prompt\n\t */\n\tgetPrompt(params: MCP.GetPromptRequest['params'], token?: CancellationToken): Promise<MCP.GetPromptResult> {\n\t\treturn this.sendRequest<MCP.GetPromptRequest, MCP.GetPromptResult>({ method: 'prompts/get', params }, token);\n\t}\n\n\t/**\n\t * List available tools\n\t */\n\tlistTools(params?: MCP.ListToolsRequest['params'], token?: CancellationToken): Promise<MCP.Tool[]> {\n\t\treturn Iterable.asyncToArrayFlat(this.sendRequestPaginated<MCP.ListToolsRequest, MCP.ListToolsResult, MCP.Tool>('tools/list', result => result.tools, params, token));\n\t}\n\n\t/**\n\t * Call a specific tool\n\t */\n\tcallTool(params: MCP.CallToolRequest['params'] & MCP.Request['params'], token?: CancellationToken): Promise<MCP.CallToolResult> {\n\t\treturn this.sendRequest<MCP.CallToolRequest, MCP.CallToolResult>({ method: 'tools/call', params }, token);\n\t}\n\n\t/**\n\t * Set the logging level\n\t */\n\tsetLevel(params: MCP.SetLevelRequest['params'], token?: CancellationToken): Promise<MCP.EmptyResult> {\n\t\treturn this.sendRequest<MCP.SetLevelRequest, MCP.EmptyResult>({ method: 'logging/setLevel', params }, token);\n\t}\n\n\t/**\n\t * Find completions for an argument\n\t */\n\tcomplete(params: MCP.CompleteRequest2['params'], token?: CancellationToken): Promise<MCP.CompleteResult> {\n\t\treturn this.sendRequest<MCP.CompleteRequest2, MCP.CompleteResult>({ method: 'completion/complete', params }, token);\n\t}\n}\n", "expected": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { equals } from '../../../../base/common/arrays.js';\nimport { DeferredPromise, IntervalTimer } from '../../../../base/common/async.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { CancellationError } from '../../../../base/common/errors.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { Iterable } from '../../../../base/common/iterator.js';\nimport { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { autorun } from '../../../../base/common/observable.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nimport { canLog, ILogger, log, LogLevel } from '../../../../platform/log/common/log.js';\nimport { IProductService } from '../../../../platform/product/common/productService.js';\nimport { IMcpMessageTransport } from './mcpRegistryTypes.js';\nimport { IMcpClientMethods, McpConnectionState, McpError, MpcResponseError } from './mcpTypes.js';\nimport { MCP } from './modelContextProtocol.js';\n\n/**\n * Maps request IDs to handlers.\n */\ninterface PendingRequest {\n\tpromise: DeferredPromise<MCP.Result>;\n}\n\nexport interface McpRoot {\n\turi: string;\n\tname?: string;\n}\n\nexport interface IMcpServerRequestHandlerOptions extends IMcpClientMethods {\n\t/** MCP message transport */\n\tlaunch: IMcpMessageTransport;\n\t/** Logger instance. */\n\tlogger: ILogger;\n\t/** Log level MCP messages is logged at */\n\trequestLogLevel?: LogLevel;\n}\n/**\n * Request handler for communicating with an MCP server.\n *\n * Handles sending requests and receiving responses, with automatic\n * handling of ping requests and typed client request methods.\n */\nexport class McpServerRequestHandler extends Disposable {\n\tprivate _nextRequestId = 1;\n\tprivate readonly _pendingRequests = new Map<MCP.RequestId, PendingRequest>();\n\n\tprivate _hasAnnouncedRoots = false;\n\tprivate _roots: MCP.Root[] = [];\n\n\tpublic set roots(roots: MCP.Root[]) {\n\t\tif (!equals(this._roots, roots)) {\n\t\t\tthis._roots = roots;\n\t\t\tif (this._hasAnnouncedRoots) {\n\t\t\t\tthis.sendNotification({ method: 'notifications/roots/list_changed' });\n\t\t\t\tthis._hasAnnouncedRoots = false;\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate _serverInit!: MCP.InitializeResult;\n\tpublic get capabilities(): MCP.ServerCapabilities {\n\t\treturn this._serverInit.capabilities;\n\t}\n\n\tpublic get serverInfo(): MCP.Implementation {\n\t\treturn this._serverInit.serverInfo;\n\t}\n\n\t// Event emitters for server notifications\n\tprivate readonly _onDidReceiveCancelledNotification = this._register(new Emitter<MCP.CancelledNotification>());\n\treadonly onDidReceiveCancelledNotification = this._onDidReceiveCancelledNotification.event;\n\n\tprivate readonly _onDidReceiveProgressNotification = this._register(new Emitter<MCP.ProgressNotification>());\n\treadonly onDidReceiveProgressNotification = this._onDidReceiveProgressNotification.event;\n\n\tprivate readonly _onDidChangeResourceList = this._register(new Emitter<void>());\n\treadonly onDidChangeResourceList = this._onDidChangeResourceList.event;\n\n\tprivate readonly _onDidUpdateResource = this._register(new Emitter<MCP.ResourceUpdatedNotification>());\n\treadonly onDidUpdateResource = this._onDidUpdateResource.event;\n\n\tprivate readonly _onDidChangeToolList = this._register(new Emitter<void>());\n\treadonly onDidChangeToolList = this._onDidChangeToolList.event;\n\n\tprivate readonly _onDidChangePromptList = this._register(new Emitter<void>());\n\treadonly onDidChangePromptList = this._onDidChangePromptList.event;\n\n\t/**\n\t * Connects to the MCP server and does the initialization handshake.\n\t * @throws MpcResponseError if the server fails to initialize.\n\t */\n\tpublic static async create(instaService: IInstantiationService, opts: IMcpServerRequestHandlerOptions, token?: CancellationToken) {\n\t\tconst mcp = new McpServerRequestHandler(opts);\n\t\tconst store = new DisposableStore();\n\t\ttry {\n\t\t\tconst timer = store.add(new IntervalTimer());\n\t\t\ttimer.cancelAndSet(() => {\n\t\t\t\topts.logger.info('Waiting for server to respond to `initialize` request...');\n\t\t\t}, 5000);\n\n\t\t\tawait instaService.invokeFunction(async accessor => {\n\t\t\t\tconst productService = accessor.get(IProductService);\n\t\t\t\tconst initialized = await mcp.sendRequest<MCP.InitializeRequest, MCP.InitializeResult>({\n\t\t\t\t\tmethod: 'initialize',\n\t\t\t\t\tparams: {\n\t\t\t\t\t\tprotocolVersion: MCP.LATEST_PROTOCOL_VERSION,\n\t\t\t\t\t\tcapabilities: {\n\t\t\t\t\t\t\troots: { listChanged: true },\n\t\t\t\t\t\t\tsampling: opts.createMessageRequestHandler ? {} : undefined,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tclientInfo: {\n\t\t\t\t\t\t\tname: productService.nameLong,\n\t\t\t\t\t\t\tversion: productService.version,\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}, token);\n\n\t\t\t\tmcp._serverInit = initialized;\n\n\t\t\t\tmcp.sendNotification<MCP.InitializedNotification>({\n\t\t\t\t\tmethod: 'notifications/initialized'\n\t\t\t\t});\n\t\t\t});\n\n\t\t\treturn mcp;\n\t\t} catch (e) {\n\t\t\tmcp.dispose();\n\t\t\tthrow e;\n\t\t} finally {\n\t\t\tstore.dispose();\n\t\t}\n\t}\n\n\tpublic readonly logger: ILogger;\n\tprivate readonly _launch: IMcpMessageTransport;\n\tprivate readonly _requestLogLevel: LogLevel;\n\tprivate readonly _createMessageRequestHandler: IMcpServerRequestHandlerOptions['createMessageRequestHandler'];\n\n\tprotected constructor({\n\t\tlaunch,\n\t\tlogger,\n\t\tcreateMessageRequestHandler,\n\t\trequestLogLevel = LogLevel.Debug,\n\t}: IMcpServerRequestHandlerOptions) {\n\t\tsuper();\n\t\tthis._launch = launch;\n\t\tthis.logger = logger;\n\t\tthis._requestLogLevel = requestLogLevel;\n\t\tthis._createMessageRequestHandler = createMessageRequestHandler;\n\n\t\tthis._register(launch.onDidReceiveMessage(message => this.handleMessage(message)));\n\t\tthis._register(autorun(reader => {\n\t\t\tconst state = launch.state.read(reader).state;\n\t\t\t// the handler will get disposed when the launch stops, but if we're still\n\t\t\t// create()'ing we need to make sure to cancel the initialize request.\n\t\t\tif (state === McpConnectionState.Kind.Error || state === McpConnectionState.Kind.Stopped) {\n\t\t\t\tthis.cancelAllRequests();\n\t\t\t}\n\t\t}));\n\t}\n\n\t/**\n\t * Send a client request to the server and return the response.\n\t *\n\t * @param request The request to send\n\t * @param token Cancellation token\n\t * @param timeoutMs Optional timeout in milliseconds\n\t * @returns A promise that resolves with the response\n\t */\n\tprivate async sendRequest<T extends MCP.ClientRequest, R extends MCP.ServerResult>(\n\t\trequest: Pick<T, 'params' | 'method'>,\n\t\ttoken: CancellationToken = CancellationToken.None\n\t): Promise<R> {\n\t\tif (this._store.isDisposed) {\n\t\t\treturn Promise.reject(new CancellationError());\n\t\t}\n\n\t\tconst id = this._nextRequestId++;\n\n\t\t// Create the full JSON-RPC request\n\t\tconst jsonRpcRequest: MCP.JSONRPCRequest = {\n\t\t\tjsonrpc: MCP.JSONRPC_VERSION,\n\t\t\tid,\n\t\t\t...request\n\t\t};\n\n\t\tconst promise = new DeferredPromise<MCP.ServerResult>();\n\t\t// Store the pending request\n\t\tthis._pendingRequests.set(id, { promise });\n\t\t// Set up cancellation\n\t\tconst cancelListener = token.onCancellationRequested(() => {\n\t\t\tif (!promise.isSettled) {\n\t\t\t\tthis._pendingRequests.delete(id);\n\t\t\t\tthis.sendNotification({ method: 'notifications/cancelled', params: { requestId: id } });\n\t\t\t\tpromise.cancel();\n\t\t\t}\n\t\t\tcancelListener.dispose();\n\t\t});\n\n\t\t// Send the request\n\t\tthis.send(jsonRpcRequest);\n\t\tconst ret = promise.p.finally(() => {\n\t\t\tcancelListener.dispose();\n\t\t\tthis._pendingRequests.delete(id);\n\t\t});\n\n\t\treturn ret as Promise<R>;\n\t}\n\n\tprivate send(mcp: MCP.JSONRPCMessage) {\n\t\tif (canLog(this.logger.getLevel(), this._requestLogLevel)) { // avoid building the string if we don't need to\n\t\t\tlog(this.logger, this._requestLogLevel, `[editor -> server] ${JSON.stringify(mcp)}`);\n\t\t}\n\n\t\tthis._launch.send(mcp);\n\t}\n\n\t/**\n\t * Handles paginated requests by making multiple requests until all items are retrieved.\n\t *\n\t * @param method The method name to call\n\t * @param getItems Function to extract the array of items from a result\n\t * @param initialParams Initial parameters\n\t * @param token Cancellation token\n\t * @returns Promise with all items combined\n\t */\n\tprivate async *sendRequestPaginated<T extends MCP.PaginatedRequest & MCP.ClientRequest, R extends MCP.PaginatedResult, I>(method: T['method'], getItems: (result: R) => I[], initialParams?: Omit<T['params'], 'jsonrpc' | 'id'>, token: CancellationToken = CancellationToken.None): AsyncIterable<I[]> {\n\t\tlet nextCursor: MCP.Cursor | undefined = undefined;\n\n\t\tdo {\n\t\t\tconst params: T['params'] = {\n\t\t\t\t...initialParams,\n\t\t\t\tcursor: nextCursor\n\t\t\t};\n\n\t\t\tconst result: R = await this.sendRequest<T, R>({ method, params }, token);\n// Replaced line 241\n\t\t\tnextCursor = result.nextCursor;\n\t\t} while (nextCursor !== undefined && !token.isCancellationRequested);\n\t}\n\n\tprivate sendNotification<N extends MCP.ClientNotification>(notification: N): void {\n\t\tthis.send({ ...notification, jsonrpc: MCP.JSONRPC_VERSION });\n\t}\n\n\t/**\n\t * Handle incoming messages from the server\n\t */\n\tprivate handleMessage(message: MCP.JSONRPCMessage): void {\n\t\tif (canLog(this.logger.getLevel(), this._requestLogLevel)) { // avoid building the string if we don't need to\n\t\t\tlog(this.logger, this._requestLogLevel, `[server -> editor] ${JSON.stringify(message)}`);\n\t\t}\n\n\t\t// Handle responses to our requests\n\t\tif ('id' in message) {\n\t\t\tif ('result' in message) {\n\t\t\t\tthis.handleResult(message);\n\t\t\t} else if ('error' in message) {\n\t\t\t\tthis.handleError(message);\n\t\t\t}\n\t\t}\n\n\t\t// Handle requests from the server\n\t\tif ('method' in message) {\n\t\t\tif ('id' in message) {\n\t\t\t\tthis.handleServerRequest(message as MCP.JSONRPCRequest & MCP.ServerRequest);\n\t\t\t} else {\n\t\t\t\tthis.handleServerNotification(message as MCP.JSONRPCNotification & MCP.ServerNotification);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Handle successful responses\n\t */\n\tprivate handleResult(response: MCP.JSONRPCResponse): void {\n\t\tconst request = this._pendingRequests.get(response.id);\n\t\tif (request) {\n\t\t\tthis._pendingRequests.delete(response.id);\n\t\t\trequest.promise.complete(response.result);\n\t\t}\n\t}\n\n\t/**\n\t * Handle error responses\n\t */\n\tprivate handleError(response: MCP.JSONRPCError): void {\n\t\tconst request = this._pendingRequests.get(response.id);\n\t\tif (request) {\n\t\t\tthis._pendingRequests.delete(response.id);\n\t\t\trequest.promise.error(new MpcResponseError(response.error.message, response.error.code, response.error.data));\n\t\t}\n\t}\n\n\t/**\n\t * Handle incoming server requests\n\t */\n\tprivate async handleServerRequest(request: MCP.JSONRPCRequest & MCP.ServerRequest): Promise<void> {\n\t\ttry {\n\t\t\tlet response: MCP.Result | undefined;\n\t\t\tif (request.method === 'ping') {\n\t\t\t\tresponse = this.handlePing(request);\n\t\t\t} else if (request.method === 'roots/list') {\n\t\t\t\tresponse = this.handleRootsList(request);\n\t\t\t} else if (request.method === 'sampling/createMessage' && this._createMessageRequestHandler) {\n\t\t\t\tresponse = await this._createMessageRequestHandler(request.params as MCP.CreateMessageRequest['params']);\n\t\t\t} else {\n\t\t\t\tthrow McpError.methodNotFound(request.method);\n\t\t\t}\n\t\t\tthis.respondToRequest(request, response);\n\t\t} catch (e) {\n\t\t\tif (!(e instanceof McpError)) {\n\t\t\t\tthis.logger.error(`Error handling request ${request.method}:`, e);\n\t\t\t\te = McpError.unknown(e);\n\t\t\t}\n\n\t\t\tconst errorResponse: MCP.JSONRPCError = {\n\t\t\t\tjsonrpc: MCP.JSONRPC_VERSION,\n\t\t\t\tid: request.id,\n\t\t\t\terror: {\n\t\t\t\t\tcode: e.code,\n\t\t\t\t\tmessage: e.message,\n\t\t\t\t\tdata: e.data,\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tthis.send(errorResponse);\n\t\t}\n\t}\n\t/**\n\t * Handle incoming server notifications\n\t */\n\tprivate handleServerNotification(request: MCP.JSONRPCNotification & MCP.ServerNotification): void {\n\t\tswitch (request.method) {\n\t\t\tcase 'notifications/message':\n\t\t\t\treturn this.handleLoggingNotification(request);\n\t\t\tcase 'notifications/cancelled':\n\t\t\t\tthis._onDidReceiveCancelledNotification.fire(request);\n\t\t\t\treturn this.handleCancelledNotification(request);\n\t\t\tcase 'notifications/progress':\n\t\t\t\tthis._onDidReceiveProgressNotification.fire(request);\n\t\t\t\treturn;\n\t\t\tcase 'notifications/resources/list_changed':\n\t\t\t\tthis._onDidChangeResourceList.fire();\n\t\t\t\treturn;\n\t\t\tcase 'notifications/resources/updated':\n\t\t\t\tthis._onDidUpdateResource.fire(request);\n\t\t\t\treturn;\n\t\t\tcase 'notifications/tools/list_changed':\n\t\t\t\tthis._onDidChangeToolList.fire();\n\t\t\t\treturn;\n\t\t\tcase 'notifications/prompts/list_changed':\n\t\t\t\tthis._onDidChangePromptList.fire();\n\t\t\t\treturn;\n\t\t}\n\t}\n\n\tprivate handleCancelledNotification(request: MCP.CancelledNotification): void {\n\t\tconst pendingRequest = this._pendingRequests.get(request.params.requestId);\n\t\tif (pendingRequest) {\n\t\t\tthis._pendingRequests.delete(request.params.requestId);\n\t\t\tpendingRequest.promise.cancel();\n\t\t}\n\t}\n\n\tprivate handleLoggingNotification(request: MCP.LoggingMessageNotification): void {\n\t\tlet contents = typeof request.params.data === 'string' ? request.params.data : JSON.stringify(request.params.data);\n\t\tif (request.params.logger) {\n\t\t\tcontents = `${request.params.logger}: ${contents}`;\n\t\t}\n\n\t\tswitch (request.params?.level) {\n\t\t\tcase 'debug':\n\t\t\t\tthis.logger.debug(contents);\n\t\t\tcase 'info':\n\t\t\tcase 'notice':\n\t\t\t\tthis.logger.info(contents);\n\t\t\t\tbreak;\n\t\t\tcase 'warning':\n\t\t\t\tthis.logger.warn(contents);\n\t\t\t\tbreak;\n\t\t\tcase 'error':\n\t\t\tcase 'critical':\n\t\t\tcase 'alert':\n\t\t\tcase 'emergency':\n\t\t\t\tthis.logger.error(contents);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tthis.logger.info(contents);\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\t/**\n\t * Send a generic response to a request\n\t */\n\tprivate respondToRequest(request: MCP.JSONRPCRequest, result: MCP.Result): void {\n\t\tconst response: MCP.JSONRPCResponse = {\n\t\t\tjsonrpc: MCP.JSONRPC_VERSION,\n\t\t\tid: request.id,\n\t\t\tresult\n\t\t};\n\t\tthis.send(response);\n\t}\n\n\t/**\n\t * Send a response to a ping request\n\t */\n\tprivate handlePing(_request: MCP.PingRequest): {} {\n\t\treturn {};\n\t}\n\n\t/**\n\t * Send a response to a roots/list request\n\t */\n\tprivate handleRootsList(_request: MCP.ListRootsRequest): MCP.ListRootsResult {\n\t\tthis._hasAnnouncedRoots = true;\n\t\treturn { roots: this._roots };\n\t}\n\n\tprivate cancelAllRequests() {\n\t\tthis._pendingRequests.forEach(pending => pending.promise.cancel());\n\t\tthis._pendingRequests.clear();\n\t}\n\n\tpublic override dispose(): void {\n\t\tthis.cancelAllRequests();\n\t\tsuper.dispose();\n\t}\n\n\t/**\n\t * Send an initialize request\n\t */\n\tinitialize(params: MCP.InitializeRequest['params'], token?: CancellationToken): Promise<MCP.InitializeResult> {\n\t\treturn this.sendRequest<MCP.InitializeRequest, MCP.InitializeResult>({ method: 'initialize', params }, token);\n\t}\n\n\t/**\n\t * List available resources\n\t */\n\tlistResources(params?: MCP.ListResourcesRequest['params'], token?: CancellationToken): Promise<MCP.Resource[]> {\n\t\treturn Iterable.asyncToArrayFlat(this.listResourcesIterable(params, token));\n\t}\n\n\t/**\n\t * List available resources (iterable)\n\t */\n\tlistResourcesIterable(params?: MCP.ListResourcesRequest['params'], token?: CancellationToken): AsyncIterable<MCP.Resource[]> {\n\t\treturn this.sendRequestPaginated<MCP.ListResourcesRequest, MCP.ListResourcesResult, MCP.Resource>('resources/list', result => result.resources, params, token);\n\t}\n\n\t/**\n\t * Read a specific resource\n\t */\n\treadResource(params: MCP.ReadResourceRequest['params'], token?: CancellationToken): Promise<MCP.ReadResourceResult> {\n\t\treturn this.sendRequest<MCP.ReadResourceRequest, MCP.ReadResourceResult>({ method: 'resources/read', params }, token);\n\t}\n\n\t/**\n\t * List available resource templates\n\t */\n\tlistResourceTemplates(params?: MCP.ListResourceTemplatesRequest['params'], token?: CancellationToken): Promise<MCP.ResourceTemplate[]> {\n\t\treturn Iterable.asyncToArrayFlat(this.sendRequestPaginated<MCP.ListResourceTemplatesRequest, MCP.ListResourceTemplatesResult, MCP.ResourceTemplate>('resources/templates/list', result => result.resourceTemplates, params, token));\n\t}\n\n\t/**\n\t * Subscribe to resource updates\n\t */\n\tsubscribe(params: MCP.SubscribeRequest['params'], token?: CancellationToken): Promise<MCP.EmptyResult> {\n\t\treturn this.sendRequest<MCP.SubscribeRequest, MCP.EmptyResult>({ method: 'resources/subscribe', params }, token);\n\t}\n\n\t/**\n\t * Unsubscribe from resource updates\n\t */\n\tunsubscribe(params: MCP.UnsubscribeRequest['params'], token?: CancellationToken): Promise<MCP.EmptyResult> {\n\t\treturn this.sendRequest<MCP.UnsubscribeRequest, MCP.EmptyResult>({ method: 'resources/unsubscribe', params }, token);\n\t}\n\n\t/**\n\t * List available prompts\n\t */\n\tlistPrompts(params?: MCP.ListPromptsRequest['params'], token?: CancellationToken): Promise<MCP.Prompt[]> {\n\t\treturn Iterable.asyncToArrayFlat(this.sendRequestPaginated<MCP.ListPromptsRequest, MCP.ListPromptsResult, MCP.Prompt>('prompts/list', result => result.prompts, params, token));\n\t}\n\n\t/**\n\t * Get a specific prompt\n\t */\n\tgetPrompt(params: MCP.GetPromptRequest['params'], token?: CancellationToken): Promise<MCP.GetPromptResult> {\n\t\treturn this.sendRequest<MCP.GetPromptRequest, MCP.GetPromptResult>({ method: 'prompts/get', params }, token);\n\t}\n\n\t/**\n\t * List available tools\n\t */\n\tlistTools(params?: MCP.ListToolsRequest['params'], token?: CancellationToken): Promise<MCP.Tool[]> {\n\t\treturn Iterable.asyncToArrayFlat(this.sendRequestPaginated<MCP.ListToolsRequest, MCP.ListToolsResult, MCP.Tool>('tools/list', result => result.tools, params, token));\n\t}\n\n\t/**\n\t * Call a specific tool\n\t */\n\tcallTool(params: MCP.CallToolRequest['params'] & MCP.Request['params'], token?: CancellationToken): Promise<MCP.CallToolResult> {\n\t\treturn this.sendRequest<MCP.CallToolRequest, MCP.CallToolResult>({ method: 'tools/call', params }, token);\n\t}\n\n\t/**\n\t * Set the logging level\n\t */\n\tsetLevel(params: MCP.SetLevelRequest['params'], token?: CancellationToken): Promise<MCP.EmptyResult> {\n\t\treturn this.sendRequest<MCP.SetLevelRequest, MCP.EmptyResult>({ method: 'logging/setLevel', params }, token);\n\t}\n\n\t/**\n\t * Find completions for an argument\n\t */\n\tcomplete(params: MCP.CompleteRequest2['params'], token?: CancellationToken): Promise<MCP.CompleteResult> {\n\t\treturn this.sendRequest<MCP.CompleteRequest2, MCP.CompleteResult>({ method: 'completion/complete', params }, token);\n\t}\n}\n", "fpath": "/vs/workbench/contrib/mcp/common/mcpServerRequestHandler.ts"}