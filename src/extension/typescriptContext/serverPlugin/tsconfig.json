{
    "compilerOptions": {
        "module": "CommonJS",
        "moduleResolution": "node",
        "esModuleInterop": true,
        "target": "es2022",
        "lib": [
            "es2022"
        ],
        "types": [
            "node"
        ],
        "strict": true,
        "noImplicitAny": true,
        "noImplicitReturns": true,
        "noImplicitThis": true,
		"noImplicitOverride": true,
        "declaration": true,
        "stripInternal": true,
        "sourceMap": true,
        "declarationMap": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
 		"useUnknownInCatchVariables": false,
		"noFallthroughCasesInSwitch": true,
		"forceConsistentCasingInFileNames": true,
        "skipLibCheck": true,
		"rootDir": "./src",
        "outDir": "./lib",
        "incremental": true,
        "tsBuildInfoFile": "./lib/compile.tsbuildInfo",
    },
    "include": [
        "src"
    ],
    "exclude": [
        "fixtures/**/*"
    ]
}