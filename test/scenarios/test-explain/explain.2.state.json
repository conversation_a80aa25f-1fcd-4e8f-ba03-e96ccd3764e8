{"activeTextEditor": {"selections": [{"anchor": {"line": 16, "character": 0}, "active": {"line": 20, "character": 1}, "start": {"line": 16, "character": 0}, "end": {"line": 20, "character": 1}}], "documentFilePath": "classes.ts", "visibleRanges": [{"start": {"line": 16, "character": 0}, "end": {"line": 20, "character": 1}}], "languageId": "typescript"}, "activeFileDiagnostics": [], "debugConsoleOutput": "Test", "terminalBuffer": "Test"}