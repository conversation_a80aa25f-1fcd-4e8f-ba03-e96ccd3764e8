{"activeTextEditor": {"selections": [{"anchor": {"line": 13, "character": 0}, "active": {"line": 18, "character": 1}, "start": {"line": 13, "character": 0}, "end": {"line": 18, "character": 1}}], "documentFilePath": "types.ts", "visibleRanges": [{"start": {"line": 13, "character": 0}, "end": {"line": 18, "character": 1}}], "languageId": "typescript"}, "activeFileDiagnostics": [], "debugConsoleOutput": "Test", "terminalBuffer": "Test"}