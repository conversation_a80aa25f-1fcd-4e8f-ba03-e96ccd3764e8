{"activeTextEditor": {"selections": [{"anchor": {"line": 5, "character": 3}, "active": {"line": 5, "character": 3}, "start": {"line": 5, "character": 3}, "end": {"line": 5, "character": 3}}], "documentFilePath": "cursor.ts", "visibleRanges": [{"start": {"line": 4, "character": 0}, "end": {"line": 9, "character": 0}}], "languageId": "typescript"}, "activeFileDiagnostics": [], "debugConsoleOutput": "Test", "terminalSelection": "Hello, world!", "terminalBuffer": "Hello, world!"}