{"activeTextEditor": {"selections": [{"anchor": {"line": 13, "character": 0}, "active": {"line": 17, "character": 1}, "start": {"line": 13, "character": 0}, "end": {"line": 17, "character": 1}}], "documentFilePath": "methods.ts", "visibleRanges": [{"start": {"line": 13, "character": 0}, "end": {"line": 17, "character": 1}}], "languageId": "typescript"}, "activeFileDiagnostics": [], "debugConsoleOutput": "Test", "terminalBuffer": "Test"}