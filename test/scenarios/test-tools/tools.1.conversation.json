[{"question": "find all phone numbers in markdown files in the codebase", "stateFile": "./tools.state.json", "expectedToolCalls": "grep_search", "toolInputValues": {"query": "\\d{3}", "isRegexp": true, "includePattern": "**/*.md"}, "tools": {"find_files": true, "grep_search": true, "read_file": true, "insert_edit_into_file": true, "semantic_search": true, "list_dir": true, "search_workspace_symbols": true}}]