/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// Write a functon that computes the nth Fibonacci number
// By definition, the first two numbers in the <PERSON><PERSON><PERSON>ci sequence are 0 and 1, and each subsequent number is the sum of the previous two.
export function fib(n) {
    if (n < 2) {
        return n
    }
    return fib(n - 1) + fib(n - 2)
}