[{"name": "/doc-inline2 [inline] [cpp] - doc comment for C++", "requests": ["36f0d0b4c72432fd8476b1f2e49ba643dc43436f1f368a40515b5e92006dc9df", "38191b97ab267dcedb90890db79b343ab0683adac5314049563b52b4d74314f7", "55a867dd37c566a1b5b4f3b6efd02887d10fe2e3e5c7bd008ce0d07afbf39ce8", "92933e6c34966697be8968d45e1877cf20e9183bc04e3958dd9ec003104f942b", "99abf21fd5becda59c495b9ac9c5c970b04d8653b212acf8113597ea3e2b08df", "c711875d4c9659495264208435c86fd6aacb55d97e5ef58a4b987a76c52c917b", "d832ced1bf37ff6d6a281f3919e5d007f234f1561fe8db63fde794b8a17ca740", "f98f60a2915958ce675108cffcae5160052dd526fb551cac9304d32c71bd1f44", "ffca36504f8d501ff71804394df02b85e2742624efc37de70a6d2bee7120b661"]}, {"name": "/doc-inline2 [inline] [cpp] - doc comment for macro", "requests": ["2d88f9b63fdda88aad944985f56de10b125e178e7cb12de8debe5ae895ad7778", "3a66d7f370ca161cc5b5fc816d24c56fa82dddee3fec8c26baf0b1ad5aa88e6f", "3c9d24756fbf3a34589120768211edd4bc0b3e3d24cf98e154c749934237fb91", "5f77e5f8542e74316a0a0e76ff214857c33886034055427d1274287aa3aa458f", "7c8917ba666265a6c25182fac9c9c21dcc44e2d7aa3d02539aa489a3bdaa7d46", "80923113d156a7b94447b986536f11ab2d09f53cdcb49b524595c6c78838a8a8", "a912b46a826cd0091cd6598f15c166ed655d834720a0a5bfe21c65e0c2213ad9", "b56caece627ad9955678cdd812e3535581efd2bf2927654b1c2db3dbe5727973", "b5a6d2aee46c8f0c143a4b0d1ee41aa23f4bb7cb6739e44b02c62cbf19695dd5", "d1f04d3257729306274935121ce8a1e9fc8f762494fee641524de565ee388683", "ed7b40d4e46d6991783fb538cdb5f4d23bb6e9d9e4e38ae79e56dd8447cf88e9"]}, {"name": "/doc-inline2 [inline] [cpp] - doc comment for template", "requests": ["1abb4b08562546766b04319b9d65ea97102c1bb3d422c3bd1b188a3f0e260e6a", "2249162ec16b5fcb498f2cc78e410a31be9be1baa6cf141ced4b68bf71367c56", "2e521b3c2cc9e66230e1f3ea21eec51b03036aa73c14fbf32c0e0a07b2cf903c", "4bd5e4f023f3bbd846745e464c0a9381767a9cdcc42c6e1e5b53bc67805a83dc", "85aabf1b53ce540650e6f9a9740d2c3c9f65abb43f49a455e7b65cf738f03fe3", "a0f94ef82a62a0689b8f10c7e01dcac134a89e0dbba763ffdd94078d82776d04", "ab3d1ec16af4d565661cd89424158f3cafe824289b0d680efbe142beb081394f", "acd6d659ac61a08e2b63d0362906908a63ee47803f607aece2f2e276ff8c4ee5", "bceac71b488d45d2963846b1517a280150eab9677f1735fb7c6802871dca4dd4", "e242ac20010f3e1333d82026374a7cd8fecc7e634924bf2b4ff66559942019a9", "e7462b3d3e3fbf164ee2639b0d96d90da00fd17dbe2b2358d2fcd3b0caf38581"]}, {"name": "/doc-inline2 [inline] [java] - class", "requests": ["27028534d77fab418c3de7780a61488095ed5ec7cc1aad0e95958eaae05b04fc", "2f856716dc60e4bf7cf3d594ed124eda17aadefa9203d12616c8f7a11ed548a5", "94f4390e3ed41de6227c6ad462c4ec8cdde48dacfe8343c6f7038713f562968b", "998536153e101d4589cdf99e7985c386fb5438a4c05228b1f6dd309c1562a890", "b1df7e5d148b0034d012302dbe1cfe39041db6efc05451cb9bf701eb622e6cef", "c66bea7309f089b7582e7cf2679982ae87596d50c0d3c32937333ae15c661a97", "e7dc62d688a963dd7c996cff46b90d76e0b8c4eae5db0f262214793907d4bc8f", "e9dad50b5869d2f77b98dbaed67d3d1d3b6c390800a4cd0fcb80b5923c243d65", "fc04f8d02bb04d36cfd2c1a950c07780bbe1df511818b9d390e0a0a6749cde7d"]}, {"name": "/doc-inline2 [inline] [java] - method", "requests": ["0dfa46edaec26e34a902d201a7ff27432ae89e2d00d068d61969f98e09138ab2", "216338a010f60ad7f702b669a0fdb406bc6a076a2e83b834af57715e415dd72f", "26cace4e0c6d1cca8cd9f72f243831a0706024396eb54adee34c972483e77e3a", "46b0dc3501d85adcd44ba89f939e1ec9aa9e2e7903c03dcd9084d0a42d0134ca", "4b83e197f8aaf595f5ebfa5e328578b425b7d0b7397ef4f7f41a2e70300bb749", "7470ff82eb8ae571cecc8199df78414a56289329c855bcf972ac24c6e4ff0f65", "84ed98db14c55643347795eeb314644a1f6e549d73111bc9975d9d0495b55d21", "9a7a2db434c0844209b34364dbbff4815de2ea4e355de21dd04a01a8e90e9ba4", "bff98cb7dbb7cb3b55881626e974d9ef2e6a0b401032d95316d826029a41cb2f"]}, {"name": "/doc-inline2 [inline] [ruby] - long method", "requests": ["4aa094219b3ca86b5102ea46377bac3f43767b8567782403c7c65597169e2bc2", "56f21ddb1595922f1f05c68ffd9f2301f72a49c13a45ed4a46f84cdedd240f50", "a35db91127716f40aefa1619d637446b1015e75830868bfb1f5d214c7ae7eea9", "ce78cd7c2c4bc762349119688e778d58dbc1950ac681c9090a975c3cd2cd3841", "d1028bca84c2f90e13f9b57bbf037f739ee658e0641c5f18b3433fd72b026848", "e6c87b519b4a959d3eb199299f6ea54ba7584050155a33119f34ff7bae130a64", "e9c5d7659a8d3313e9666d9b3caca9d622f62280aa228b835d095c2d783ee9d4"]}, {"name": "/doc-inline2 [inline] [ruby] - method", "requests": ["3085a432109006d786eb93ce2eb496fb4659171c75f685226b69bd5dffcbb0b3", "55dc5d1ce04ab75c52c9a1646ad21d91141c26639619cc7ffcbff48e548cc333", "7d3d60faf4e342a03c57c6b63bef1923d3338f1396a32e5cbd3115f67e5188c2", "90ad20e4f00e2d130bc4e0700d8c1840d47f94de1432fa04956a4131c11c9530", "f71ead81d70c58494ab7c86f9efa8ed0463df4aa62d665be7820321af50a6964"]}, {"name": "/doc-inline2 [inline] [typescript] - able to document whole class, which is larger than context length", "requests": ["10b31076cca1274160880a577df915f6ff265dfd0e093a6b269b9c2001a1f2f1", "2e0f6df19c27e045e2eff01d442930c2269d9c05da3bcf83b2f6a9b6b2f8e800", "3d41bc097b272ad2be39d5e5aa1b6cabe72559815cdb9c6c37dd6a73efad2f61", "484347a6d6a1e8d0611b2fc188a20751d473ba903a1f32d68fe914936684f93e", "49e561568b7e5293ed621e7f1c86328fc7c2ad18a3b6354c9d3944d56a2143be", "49f4023d85187ffef695226ef4cddd2689cd427ef5ffa7a0f43a29f1ee690689", "620a64211fee00f765e0d2f22e5c56236437377cc278c290f6458a935e61b342", "6b4b48207a65d15dc3fd5ae001df42b67b761e887bb07a8ac7c0f06cb75b2459", "72a32f62e7f5b9d795788c8609bc5b7ed762689da6eb7ab69dab1cd8d78826fb", "b18bc1eeca08036831ba24e1bb80b22ff701e3dfad74e723b476e29a26f9e64c", "f3507df13dc6f0e46542eadd275d574828cfb00735071aa3731c6e01146bb958"]}, {"name": "/doc-inline2 [inline] [typescript] - class", "requests": ["08ff221e6dd06a1ab22d7d28fd1ff13297c3c598fb4424aa49befb13cbacb4f3", "0d5237c30142ba16a257a545089db51ebd2060f0215db4641c719d6df649b266", "1628d3e1ef2f3fafb144f27e21d61caf944a6c79e70de75f207c4fdf8dc1c630", "1ca7638ad9d41757b8bfd1061e2e427e5b16a4f40cda1bf06a3edf93b95134d9", "601e6780da973c8e72e102260bb8cb61990f5be75c0a18d1f03597657c5b8145", "608a8519d2f884f2af8fab1640c23948a5dd7f2d3c9b72b0b94843291b80d53d", "76ada181518921f779fd323befd991b3b813b960fd2a2fc09c415c92408706be", "96b6371304f8e37cba8b212bc0deb64d492fabd43985f048aed6dfe190ce1791", "c78400301d8ab2b557b74e99e2284c74784cbbe159dcc670cd94d963311fa343", "d938aeaecd9ca6843e3e76ff47c94a36ab1c0e840a0d6be13f0b69a07e4add86", "db510c20e131c8235ae999e1886d554f95ec146f9fdfd4f09acd7c5efec9f943"]}, {"name": "/doc-inline2 [inline] [typescript] - doc explain ts code", "requests": ["0782a0d8d219b835741fb859c6e67925a91b38b3230b943c96e6a451573a5720", "0fbd07f229e9b191c7312bb39e55e55a79b316483f30545dac4d2a392d2f36c6", "1ad79b83e12013600cde955b1a049a8f02fd7bf713c0e81c78e2fd6902f76a4b", "4314b5b5895ac36e375cd35cf2645152f65f7b2a956202470e507612c8263dc1", "53d3efec8707aeb82c9a079c2bfae43aa6c25ff01559738557559248c1d42387", "54bcf95f2d6d42b12f8e0dc6efcbee18331c231b06eebdc45bf9da5f39cfb7ca", "6350f32727e1a2ec94bbb3932bc4100c4f7a85c75dd66a68bd786bdb35d7de37", "6d7a005acf05524830d87fc7221402129b066f34d067049af2196aa180ecc434", "8e2521c1fb541dc7981e3f1b1e9c282f622453d6cbfefc82f2d48db64e8f693b", "ca527dd92510cec6634c806e8e7be70474bba478b276b6a8f86ad93294c7146a", "e4ffae15c6a87f3a8978bbbf814467bda62180b8341e486d4d79a9ecc650d902", "e897006a5dd4c8399071aef7eb823037b8dfdc528eb3ff3c091a3c3d3933a1e9", "f6af92eef912a21af6fc54db06cfbece4d62641a6830c2cfa5149f72b6873f68", "f92574d476cf7665033ccfebd42837bdc7cad803820b4b9b8a9e30d70e345bd4", "fa7a0a1ca4d669526a2787fc33f10ed476419b87a4a62e10b9d6b004d4307daa"]}, {"name": "/doc-inline2 [inline] [typescript] - does not include types in the documentation comment - function", "requests": ["0c456066027b1b68ef4ec05bf5d17a29f9e9031de26cdcd749f7d90193f67f52", "1579bfb1f747cd2b349c32db4b3ff6f6789fdcb5c9c34147c671e0879079867c", "5e1eda76ab874c1504519c6df4fe118a14a4c283824fb1cafa84cb32c72b743f", "5faa84494220eb0983b9dbb4adca50ae61ed656b419c972841bd1c0c26f416d9", "c434c8c5583fe669a468dba91a7c8846323c45c8a53f7c3afe513f00f185b2ad", "c4abc7e18c63cfd02ce04b5ea286848630e0f374e2ac470d0003d990e84b2cb5", "d3904dfcad7c5dd3290860f64af7d736220dd266a0f0fece2b9232a52e958bc2", "fd33cfdfce0ff701d29506aaa87ed5c95917ac7c0d7778e1d35415eb8435b42e"]}, {"name": "/doc-inline2 [inline] [typescript] - interface", "requests": ["054a6bd2781b2215836e9488a8026390c3b93f937ca882af6b1946f10a2ba009", "20d12cc9351734358ec6d024673d8680d959daee187c26a3037414c02cb68770", "24e45214bd0d80e01ab5d526e14f3dc23a76d51bed235ff978fb806340bc17fa", "38e6ca9a75031e0acd900c5f16850e547674fcb0d0b55009e75930662362741a", "3bd90989943d8d89c69d16511a38847e89b5325ca62f80376b00855110ff9754", "5cd8933358eb04e84547fbf5319c6501e0c7faaa9f1fa5a210695f7ea5cf7f34", "661e21c9a993e11fdb6eb740390a02b2a814f145e1530c0abcd89064606a8219", "9d05b198b2b46149bee87b2c9e39e60564d6464dfd071c4d4ba4940234e6fdca", "a48c7f92626e50baee627e4ba04b07040d030af354e531115e9cafb58283155c", "f0594b47a7181093c0a5d041fa38629917ddb94e358ff5e0aa2ac8db37048432"]}, {"name": "/doc-inline2 [inline] [typescript] - issue #3692: add jsdoc comment - colors.ts", "requests": ["22457740c947cb81ae4da81189eed9282a13b3fe0d79335059eb79408ce0b821", "5bc6cb0ed50f5e02f49397ab7dbdad746745b9d45f92e0e9bd6b188d02efa594", "a7c944f6eb6dde06ccfdaba18dec65dd029ee322af8786f2ce37318d68c23492", "b278fa40ecc3d52a8dd3226208aebdc87446a0c297e00ec72e6767366138090b", "b5eed6771bf42296fa0bbff827f2a9b2ad2034697e3593393e1f736772ace994"]}, {"name": "/doc-inline2 [inline] [typescript] - issue #3692: add jsdoc comment using /doc - colors.ts", "requests": ["034fbe694c9bebb4218567c8bb24d689d2c92dde1439e2a6502d42d7d46011fa", "18f648c0908ffc7325034cb579c4fd0a51ea1781d5f246af221103ae070b486a", "604ff1fec0cc6a4a0544845388c1a878675c9dbeca528a39d3d328dd93ae74f7"]}, {"name": "/doc-inline2 [inline] [typescript] - issue #3763: doc everywhere", "requests": ["049e44120727d3860f63bdff8b73e36b3a9a72384fb45c3d6a97b50c6183df8f", "3a0532969a7307e7d73bbeca45e63f95fc3634c1ed61cee3ebd4352ef614ca48", "5e912c726c42acc96c73e61dbd091b4366d550dea8cee33cb39e50a37ea04bd2", "9e1ea8fcf36bbaf84691bef6731ec683be1c6adc70fbc463099236422b376cd3", "afdc6ab0d6d06467308c8b31cb066336086ccc648a37e7071d1cbf50882f32d7", "bb29ed38c610a5592227c0ecee48deef136349e722f0956820b409252afe2f15", "ce6fb8558e8721d690c11d523d20cbdf23b5123776a9b0bf021fb815eee0b135", "d88b446c0c8e7e47236a745cc8749cbd836068e68ef9fec75488e3b2cf5cd039", "f84a3df4e276eca4e9a343176aaa2f3d51437880353fc228898404f0e282c7cc"]}, {"name": "/doc-inline2 [inline] [typescript] - issue #6406", "requests": ["15889498effd4944d11a70b9dbd563d2250fb796f20fd335af473412dd3b74cb", "16a622f5a5e84b4af059354396916566429f24cab833ef7f75b6343cb2cffd7a", "37e96b620c52d985cd47e9e430b9c582a0f38d6ba5fcb0bf105771c47110309e", "63e074fd3bb7ff20be45083e444948ecc8054dd3f4912ba78491c2d604d78d5f", "65c7d360e3951315167a3bacfc8509c9e74e1d919949cb78dcf656e3c70461b4", "7b9821e73fcf254c15d687f6e91cea637b25db08bf4b540915ea7af766e28190", "94b84309c0ae2c11c1cccb1505fabfd0c427424ba0ab1f6919280148865b12fc", "a1acd0c31aff5bccfbc03b1487f400310d408f351b128c2d80f4f6ceec34ff53", "f24a57cc03b4946e2cde53c8f184833273a52160fa322b10c3c97c4ad6b4a765"]}, {"name": "/doc-inline2 [inline] [typescript] - large function", "requests": ["10e6f8c622530bdceafbc989e560f2ae5ae54680cb574b2a8ad9e67b4b7315cc", "21b2ffb05205d1ed9058b681755f9128572c00c15f67283cb019b84c24d07119", "559c6926a45a4262834cd80c0c9e0a68dd427054f2960a5d190878363575881a", "aa5ba226f76cd00b4f6c0ba02ded8aefe77c9d9fbae7e32c9cb7b3e437ca642d", "c441f4ae765fc4b91da41bf1aa93e7a3a225d64264979643097fa8877de2dbd3", "cd6e6f429b6ef0726c53036e9664614c7377aa28e6b19dbe3be64b0fe284cd4c", "cfe39e584608897396bb33633ade7124b0c7fc65752f6c5bdd2092809dba80e4", "eaef4a6516ab1cb6f0897ee38452ef2892b519ce9e538c8f206b0bf99c1bce7f"]}, {"name": "/doc-inline2 [inline] [typescript] - supports chat variables", "requests": ["06b07ded82b156df1ef97bc1500ce5a0a3ac7d3d35b9d8887b51798255baab45", "2a634e8ccf143a2c8fcd116c472b9557a4bd1f5ade92875da4571b94c7504012", "62d63749f5e447b49d59ff83c291a36962ad01bfa57664d936ae99f600bcc353", "c6c8fe14b45b3b32f4bbff246d46c64ee2d282ae5b7c52d868b72ce4d8cbec84", "e0bf106890a3f0718b2528999d2617606666b500df1ff70eecda50e2ecff9b2e"]}]