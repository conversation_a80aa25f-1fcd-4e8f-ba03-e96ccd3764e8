[{"name": "Debug config to command [context] - cargo run platform-specific", "requests": ["0a0ac5fc124f816803b7db6bb40f4e109279d18234b4b456e393453c29f797e7", "9740ee5bac12acd1e647a977f488d7eb8ebaf724d67ad34654943581c11ae6a2", "d955f41a87147e03c8c3231921077f59f3752ba5180a23534d0d99dc53eb9f35"]}, {"name": "Debug config to command [context] - node subdirectory and arg", "requests": ["035d488ad51c23dcb9268fc11eafab94a5b8734481310666accb07e5caa2d3a7", "30689cee448aeba287dedaf1dc87b7664eb1d9ffa469e40bf6ca836267a28b9e", "9c0a6bbc8083d529c9d9b7a2fac81be57f2a3fae1898e1e2802ea2cac6636ab4"]}, {"name": "Debug config to command [context] - node test", "requests": ["2ced38655d0b31f17b6bd5e1b0e29eaa37c311683d43657c194ce458b5c16756", "51ea24eb7de990c4ba1192225fad005fa038253a6cb87e005e1af81334efc399", "75e1b0184dd4e849ee7310828a3a04caa7faa4b4034db284c6aae68d8a813cac"]}, {"name": "Debug config to command [context] - opening a browser", "requests": ["10032a20788271c94bd0d8574b0258604e12427f49caaf1948f4a14c98e8574a", "3f1a0ea5309645366b83a252665166b3a718978e0c1f89a709b151c276eb0dac", "b3f39de56857d9753eb7ee94523a742012ec5ec819a4590011671da0d102876e"]}, {"name": "Debug config to command [context] - python3 subdirectory and arg", "requests": ["471730516cb3c4af73f845a9f45da10366780f5155a2623371a50447d0b90407", "739b0b98d04b8bbf416d711c79d8223a6bdfbf146d3532f617eaba535040ede4", "ccea634e016cd9651fa0b53e28ab5334cc91af5459c2bbd06d7c8a7426d0ab37"]}]