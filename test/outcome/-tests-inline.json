[{"name": "/tests [inline] [cpp] - can create a new test file", "requests": ["807fb39765213112ad8bd357807a62318167821192b2573819eccda89cc0a907"]}, {"name": "/tests [inline] [csharp] - creates new test file with some assertions and uses correct file name", "requests": ["790c1a41b8ca523c4e333127be7079e136d25349cb9839fab7cd8ef538cf6cb8"]}, {"name": "/tests [inline] [java] - looks up existing test file", "requests": ["76312def35c6367df16ce4dcfb107fe03aced71b21bca7e8f61549399ad88ecd"]}, {"name": "/tests [inline] [java] - looks up pom.xml and junit framework info", "requests": ["842c823f76fa575479011c9d55745da043fcc67f4cee6226c16e8dacfe2af6c0"]}, {"name": "/tests [inline] [js] - /tests: with package.json info", "requests": ["5e7cf6e34f88f095ab8bcc3459534a9a2bb5828218c107407233eb2280343d97"]}, {"name": "/tests [inline] [js] - add another test to existing file", "requests": ["c0bc6ca5a057a6f6dfaf0cd5f932f5d16978de40bf881c177ff115736610f876"]}, {"name": "/tests [inline] [js] - generate-jest", "requests": ["5ad525695ca96178b2e47ef3a4f897137dcc9ce7e7d95a49215a5d924b509eae"]}, {"name": "/tests [inline] [js] - issue #1261: Failed to create new test file when in an untitled file", "requests": ["663353682a5961f38ba2824c005f96aefc9ddd9fa291c7953a076aed46499930", "7a3af40f49e654123a263f360b8e740fbc467e80ef1c365719ded84102776946"]}, {"name": "/tests [inline] [python] - focal file at repo root", "requests": ["b876a0eaacabb6a22c371cda76a80ed78080415559c69593650cf6ae9ce1cb7b"]}, {"name": "/tests [inline] [python] - parameterized tests", "requests": ["f7b53149456c3a69e3747afbbc726b0dccc9a988e1fe09a2e10aaea815367751"]}, {"name": "/tests [inline] [python] - py with pyproject.toml", "requests": ["d81bbf5305fdad664dca7692abdd355ecf420947697a7ff88559c2c591cf2b94"]}, {"name": "/tests [inline] [python] - python add to existing", "requests": ["215bac29c74c3cd39e18a7b7841edd1c3099111df5c068f387758d4c76dc7466"]}, {"name": "/tests [inline] [python] - python correct import", "requests": ["8c76401b658568c3b48ec6889f668848170b84263ae71452f96fb2df310ef570"]}, {"name": "/tests [inline] [python] - select existing test file using *_test.py format", "requests": ["552ca0bf9149a5818cb377ce43841e78a9ff99ab9c821969afd64bdc3c1d1fd0"]}, {"name": "/tests [inline] [python] - select test folder if exists for new test files", "requests": ["bcfbb443cb764d3e1c614ec1ee7e301ab19fb8bfeb161a4a009a15822f99654e"]}, {"name": "/tests [inline] [python] - test with docstring", "requests": ["47d39e7707713abac14f54453a81062de390bef9b1d8a4743547d2127c1c297c"]}, {"name": "/tests [inline] [python] - update import statement", "requests": ["47d39e7707713abac14f54453a81062de390bef9b1d8a4743547d2127c1c297c"]}, {"name": "/tests [inline] [typescript] - BidiMap test generation (inside file)", "requests": ["546f32bffb7f6d9824302ec3c372b3e2c42e38ad2ed52e54dab618625da4d6f7"]}, {"name": "/tests [inline] [typescript] - BidiMap test generation (inside test)", "requests": ["6ec2b183d0288fe8130190ad7b264c73ff1259012b0fbc68c252ce94f207a798"]}, {"name": "/tests [inline] [typescript] - can add a test after an existing one", "requests": ["01da33c68482b025da7b42c2ab0daa5d0fb3d4790d7bbf910c5216b73bfdf8d2"]}, {"name": "/tests [inline] [typescript] - can add a test after an existing one with empty line", "requests": ["6e174bd690992c3bd968bae5204d5dd90757df5b1f5774e38869d3cde7dd8bd5"]}, {"name": "/tests [inline] [typescript] - supports chat variables", "requests": ["70e6531cd5851b9a32d56845bd442c4e3cc788e8208e5d19e6f267c96a1f9013"]}, {"name": "/tests [inline] [typescript] - ts-new-test", "requests": ["0111a91b6ee279d87b3b8e4400cf024749293c387157f05145bde4a6026af691"]}]