[{"name": "notebooks (toolCalling) [panel] - Edit cell tool", "requests": ["b327f04de3daa69fbcbcf0205f1ae3d1e8e7c9d34066acdc71f9786d878386cc"]}, {"name": "notebooks (toolCalling) [panel] - New Notebook Tool with EditFile and EditNotebook", "requests": ["68e7604f5ed2dd02b4bcc849deb01c7ea951f5641382ed0c9f7ecf44894d4a02"]}, {"name": "notebooks (toolCalling) [panel] - New Notebook Tool without EditFile and with EditNotebook", "requests": ["68e7604f5ed2dd02b4bcc849deb01c7ea951f5641382ed0c9f7ecf44894d4a02"]}, {"name": "notebooks (toolCalling) [panel] - New Notebook Tool without EditFile and without EditNotebook", "requests": ["68e7604f5ed2dd02b4bcc849deb01c7ea951f5641382ed0c9f7ecf44894d4a02"]}, {"name": "notebooks (toolCalling) [panel] - Run cell at a specific index", "requests": ["ebfe212b1adbed63980491303a431e470a0fb7d2209eb63db9bdb5c478908c13"]}, {"name": "notebooks (toolCalling) [panel] - Run cell tool", "requests": ["ad01020650bd8f9cf58f8169952c1117c3d054e38eacbb07683fa278bf681bb3"]}, {"name": "notebooks (toolCalling) [panel] - Run cell tool should avoid running markdown cells", "requests": ["7e74d90e1e8ca7d105ce9712ff097eebe578b974e7082f4be8896792650c3040"]}]