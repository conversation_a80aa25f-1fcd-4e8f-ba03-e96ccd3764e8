[{"name": "multifile-edit [panel] - create a README from two other files", "requests": ["164d0bc8388e359d8ef9dd39183910b8c000ad72bee3c8cdad004631b2f19503", "23085c0be247598aea93e354f2e5c4853056702042d887ea38f61c179dd34669", "26111f2eee8c712536071238eb95187e293146fc538e3cf1c6e0e6be4d04ef7b", "53938fb615b4d1f1ffc288802854ca9694afa37622fc83af729bca79c935927e", "70765b88eca3143e95288e5dfd539064feb5a66c8000418de312c206532c6ad9", "786c5a7fa8579f34ee70e08f33314fc6347f73e298de33b86f8d931243071ed1", "9c19fd3b9fe1d97671800f2b53517c19de9bdb5ad04d81b78eb0f6a255e341f5", "a68c68d8eea3606fd9418fb318c35174f79d4a7d3328ed6e80a1e15aff500d3c", "cd4fd6de5a4aef03728bb1626e21913aa416503f1a5fc7597fc56ce58aa72007", "d0e046960563f456f9c3b4922018f5ba09ef6ccb203638795a0c0ca07eea71eb", "e35208794e2de204ea3b1ebbe37c3a728a9afb5d93d60b4dda52240df0acdb5b"]}, {"name": "multifile-edit [panel] - issue #8131: properly using dotenv in this file", "requests": ["04ede0d81a67eb1f03a29183690dac9d339256c4e79cdec5f9d7ea01209bde2e", "2096ba74578cb40917b223c8f2d02965f8887d1df1e4735839043650684b3b18", "30b664eea47139d4c3a80b6245f2bcb9a8d7cfe82b8c06963a2de7fdbb8dae10", "37d75576bab465827151d6f3ee66d010503a6f2047a5886da2d2de4460d5ef07", "516dd8d616bda25f679669c9ee3db07a1acf2ed25c26a7c18be76ba80d05d3a5", "67b696be82748de2c7f82d7e179584f84673c628e8aba4d41265e8d40b449bcf", "8d23218c7521bc15294323ce1415f9d9bfb151a4378c0a4e094dea26fcdab5a7", "92ee140e34f2172d735cd4b2c7532138e827f7496035fd82fcd5f3fc6bbc36ef", "a0dc9b9b3192ee1230fc5f177b8cfe72b0422d3e8e1b268537728e0d024febe5", "a4e14a9cc7bfb2c25dae1627c3eb23d94383943a0214ee05b8f555f64401b8f3", "df5cb386cdd3102135be45613f726a141e5710fe480e0dc2af9727bd01da91ff"]}, {"name": "multifile-edit [panel] - multiple edits on the same file", "requests": ["1354c5e2bc2b7bbdd553fc8bbc793c257e96efaa82ffa65b4b303790913c14c8", "22a588e5ed5ddd21c3b41862d32732aa00c61f6caeacd14203b55ea38e0cb514", "256a05bca3ed460ab10666fcfd2ae283fe6e05fa6647831623964af25660edce", "64edba844a21f63886dc9f48623c4ee462aec660ac932a425a00bb7b5be53729", "6d99728eaa2079c9f59f29076b5769f20776fe84dc4e5b24ee07842b47730d38", "791b1cb2ce276e021fb84db2fbaaf6aead9a289335636e03648b3845ef31c9e2", "7c61a30ab86974f622362b43d7eaaee127ad08e496c47aecaa210659e9e55d85", "8f973e4935be9fcf75d2b4f5de22e168c26b5ddd588304fcc40bbafb3c616ba3", "9009e26272010fe1d5f53123d65328a6cb5cbf9e1c092a1780a7130828013a28", "91fc3cc864458858b2df829eed52d6a765268f38f071921cb917bf0b7e1a04e0", "99c0bc2eb449b11e1d4d05e63d924eea553e84dc7d2cdf9b107a9016521a4241"]}, {"name": "multifile-edit [panel] - multiple questions", "requests": ["2a04742aa85ae0f345ae4b110edaa3d45441d708c707adc074dc4da6d5d7c080", "30468d214e98207d82723fc5f476c34a7b8e94e0aaf7224b9dbc2b86682a0bb2", "4ee18846621a329a881488daecf3afa2b471410e5ae464d581103c7b289a0687", "5ef699c87bb2bc50ced5ef8aa0437ea05e96a8266771998a677267c78c8ce8d9", "8c7f6dbde8fad848b5c3760eb5da9c26d363c053834821429502baf3512f2fb5", "aa3ff064b3bbfaac8a4bf139ede9b70bf04157346ba79dd952dc8e37c298ea53", "b2af5827c500b80c1d06153d03ed4ed3f3e1d59dd7d1f118302e9b4f68027e63", "fde99269dd28448d3adc2000e8b45719372e6caf6f7daa3da38c8565728a2b1e"]}, {"name": "multifile-edit [panel] - unicode string sequences", "requests": ["55b6866287c7860f22bf308bc02ba94efb556201ca52b553289455e027eb4945", "945360e2b63c4cb68dfdf97d0ee90f638ff9046b9f86c224a2a3959aa9020b0f", "94a896757f25f9711a89e72efe8019e72f5fb74cfea703e3f4e65cf5b7f47a29", "f05754483c8f475f2c62bfc1ca979337ecf71db08520ebf9426cfa114d83b26a"]}, {"name": "multifile-edit [panel] - work with untitled files", "requests": ["71a7f42b4a7c98f3f99d7451b7fa4e35400460cacf4bc5cd747de143a47b78ed", "866125ff6ab7640cb54989d6f1610a29eb60b0ef5c70c64806f922566c5da575", "8a57e66a6be419f6705fc7fdaf05a2f179c9f18b4057994a7abb1fec98367805", "a646048609eb93a307d86694246ad09155628c48549f7384e19f6ac6db7b35b5"]}, {"name": "multifile-edit [panel] [html] - Edits keeps editing files that are NOT attached due to temporal context #9130", "requests": ["48c1711e6d61465feb43674c62793023787d3ea2a100f50f81bf1f22a0ffad59"]}, {"name": "multifile-edit [panel] [typescript] - add a command and dependency to a VS Code extension", "requests": ["03307d3e956761ea5f17f81ce7387180d923e56f6f3249716c28a110e4220f00", "0c4fdf129d4bf8f28e7b7299b9c77aba3399fd595b6c7dc0d2bc22ccabe7ddfb", "0ec7846b29ba76e6091de07ce6912742d993709c75092bf2cf3c205daa39bbcc", "1045c57136cce1648a85853c46fc4d306454008c24708cce607417dc30e737e7", "2d6842a8f0725f808972621c06dd4d87564b80610bbd589467cae94756134ccd", "4061b577f4ac5e5ec29b59fe6992b1b3ac30b0f364911a3d8d17a51503844d0e", "466908570ed6d0e1690c72c334c84f63fc3c3869b88a7bfaff0e59f458f40928", "62cbcc5dd9da37c38f71e77701329e735240b7fc647041b53d67bed64787a70b", "6c72ed71939852c6290e831b814a42da205aafe40b6d1cd7cb6fb12ed7188c32", "6e3692ae7b8ba89692bbfc6d3049c6ab7caf4351011bc713c5c04d6a9e1bbc94", "7cad0954b91a4c9a0215ebe339d3d5cc2f3e4536da3f801e5fc42c0443c62045", "7cb60ab8591ffd008bfa78e03691bcb3f3deac6e399ce1637bad4a8ba5f484d7", "8a5df1b97e52693b833276a10478f83b78b07b92f067832e9eb5d6e3390baf6e", "8ab89902356a73a034d650287ed548bc63642b83d8363bd8b6a73b454c4bddab", "9fd3b38418165bc4878a41b0586b61ecf072a9c9a691ae67b81ee0dd9696802d", "a48a047eaa34f161b4ab8e93b55921d004299f15b22a626b8eecd5e5232629de", "a95c675d4e527cb19626197a3b82c05d1c22bed07d0b835a72d5afa93e7571ba", "abf3292bf7263244148701ebe736552fca2a5f0b8e5dd35124470f950f251cc9", "acc3a79e6bd2153fb594e79a6c2892aaaf4c57037c51acd15b73c5cac7e069fc", "ad15368954fd956a893f8293c9639305eebed04f551881dbc246d2538cf32713", "ae0888dc041985d1a9a82e2a979dcc667f2d4a751a9448076acd8087ad8df477", "d11b3594e95893238234a3fb084353f45b2b8ee9733af585d7f7df8e5de25b22", "db9809b066099f84e4cb6af0703d1837234504f41153c7e00a943a996dd102b5", "e5fd4a8d5f76a26f8c41b343d5dd62aca84ab9d3281e46d3dbc498d4116c713a", "ee5b85bd0dad6fead8c3515676f9c50694a749828e7e7f89bbbc4a3c89d7a480", "efdead84026457583cc196421e26df44c392bccee6d6a9130f8c1cfec1464c3c", "eff1c8a24817c0e3d0b07fd7163a488e985ba5d9ddb4e2eba32b1c2816ed3143", "f1d7122e93511415254905b897ba1b0333a3311f74b8c7e6d87aada220130c84", "f45d4d7cc30e0560769d059666a4b6203ab8f4d14739729b3189af9432b34c1b", "fd25a5a5a741a83cfb1d10a84f0d6656a684ecb7da89982999f8e7725c89e342", "fda8f072841ddac8ca40f723063f8e726146a654dfe6868f0674238c7b01562d", "ffbfd670b18f12e5cb4a2106599cb29b4d20a8e7efc8030623411732a8048a26"]}, {"name": "multifile-edit [panel] [typescript] - add validation logic to three files", "requests": ["02f345b87ccf6f7b8546c0466b5959c6a539a29b0292629edc54e60456b566ac", "1fd47b7b093b66aea510a8740898136824d8c805238b5d24c68dfecb957fd06d", "2d99ef893bf445735aa2436985f8f161d1ed2be21d3bd17a4722476b7db9a164", "2e03b0daed8c628c53f0d0ea7cd188e48bf94675754d384269e28a277a4ef03a", "2f565b2f4e80d6c46ff7b05a6e548f3757b150385e5e8efadc50acc4b757ad2d", "3166146c15bfb765e7fec321f9a0cba11cfbcffe0221fce95e29abf1cd3a708c", "32e982dea6d0fd96afde758e7fcd7c1cf3c7bf58755509546ab5d7f0a01b83b9", "39ea4f0b6915fd3e782b6c18cb4b523482f5a945525ed53e4e21f89a59dfe894", "4733aadbc5875705ea186731e9699698fbd4678b35698e67b9c9ed01099bf3e2", "5130cf326c09668eb91baff1bb78dfaca3bb6e18d9bbda4d48824f3d634ce90c", "564518d957954d4b056b502fc7ff3eb05e3317b46e53cdfe8b90c8df8145e5b3", "62aff6f317429f4d8685f62e5b76e65f4f053cfa769b4fc1c50e0e624ac2e0a2", "649bf67002851d357aeb3dd81524a952c7f77f763a847ce4db726889bf12122f", "65a46a1df19a5fe81fd0acefb8fdaf539fe8067a844e75fdd3c454f8a1d0e52d", "72ccb01b656b1ad600e1fcf41177e071b2eea2902569e9d18005fb50ddf8e104", "7b3f753f15604a6b9a514b94d084100f9fab34758c5018521ceac29bddc38b0f", "a2b45d42d2e89f5a5e9996e0e98f27ceaa57cdc4676ea0e90db2b3e417dbb913", "a42a1226b82ff8b78de29c8e953216572c061ecc0d9ad7ba903d6915175ad395", "aac2aedd112ea459b607c9da8ae808eb5c6bccea89860cb9356a57828b63228f", "b606fdd229a5f004f1ddc6219699c0f5f0c17bff99346edd2db8231c2b1b30cb", "c054bced0dfe6269aacae4b03646ab0ef879830d8d1a92cc01281f16a9d76fd9", "c6181b9a5e0c4f9f20220b9490f2834a8cb7f2e446b416488c9f31864ac5bbd0", "ccac6134925260dd00150ad9928788eac3e3376b8acb05c73513ec8cef768b6b", "e4b15c67babe77cddf942318e8b3ba43ef5ffe9b7864787655e5f397c51120e8", "fb7d76812551cd52472d0730103a2686b5543000909614c1a8c92f888ccf4e37"]}, {"name": "multifile-edit [panel] [typescript] - change library used by two files", "requests": ["09291671d66bc6f32040c5a99126633cd3ff954ce87df8b337c227af43ecdbd7", "118830f995a9c2ab99d6d616f2e2a3a85a89b814c1d26c2f46a86423d5501186", "1298b2a07253ed4291dc7aec35dd5e23c0c2b8dbcf06a5c08ab6ec63643732ce", "33700e00b7c65479e3f206a83425f32feaad4fe33be63f3b3a245fd9f2b4930a", "435626ecaa2ab33403860826a2db9b0c432bd611c1c85cd959623d4e6fe92fc1", "4ada4b0507d77ddb2034bafd7fcf54f03efdae800b6e9b8b98576731fc190d17", "60bb0951cd28d486dd81b04643b55fad2030b48a6db99823b11fdc293f153213", "70bef1ef6c875e67592199b4cebfe4e344037739936bac9dd72a97dcdd8b87ed", "7cfb4885412a2b95412d114489748aaeb419e4c440b6092badd02551086e9399", "81e0edb194749067d41ddc731285ec5619f9f67d677d39805e0f6d09034ac635", "9411863a706b62804ae35c537179e5f167c8e36a7782c9c860b8ea9f7677e9a1", "ab1b62355c6a034b2af80a4194da91bcfc789243782ce9feb64cfc811b452e34", "b410adc978b9c1a2f90248e21c430a20eb3e37b94c0cd282cc07261744051414", "c116349cabf3a8fca56dc74b6bff8aa21f04a020b0489fd891d923480e51723b", "c2bb360e1e48caf3687b5b0f23b27a8a082db3b25f0de1d778fe81f7851aa6ca", "cfcbe39c9b7e7d53e4f1b124c82b4a24205b7098ba9ef45e20b690cbf54ce8e7", "ea6da98a2a4412699a335fa6836c9377a480d8b7d2e881daf00a63c8dc9f0f2f"]}, {"name": "multifile-edit [panel] [typescript] - does not delete code (big file) #15475", "requests": ["0aa627e9ede141538bc6a8bf40d129a6ac7a6db17d5060507deab36646a2a275", "2126e6747cd7511c4cdb298baef05993ecc4f2ee09570c1734bb7ccdc211a15a", "26a898a0d78d1ad74acccc3f3bc0879e5519b5bc68a15a4348bc33213f3d2273", "2f486f5e5f128e82fc9a47c12e58a7dc1078cd6e90e2a49932c27fa0d384449f", "45055625e021b89ea1b9c924be8c061bfcb76d89910434219bd8a1b489b8c5b6", "5aaf4d37d271cfc42cab8e8b24987b3020e020d3af9f7626394aee9fbfd18fbd", "6e0afddc8665b2c1eec4ae90ef7c625d46f28e5f18f5f502bab969b8d6d45cc2", "6e21e2acfb0e6b3514425392b4fba26e83839831e8e03c3bcc9425dbcc22c527", "86d19c8bd8702d744993d48e436d52a230f7e7856fc95fc3317151f211d9903d", "96d8c93c1e76901846dadbc7cfccf1863ece84b5c1496bf2556413e71cb2febb", "9e4cd1736ea00e240f07dc2696c880e9aefc15422d61a59aad7abad88b775655", "ace20086994519aa71012a3cfe39246fea7d0b596011095d80aa95793dc49929", "c6250fce0fe8c8f075a13c11a1e0fb5b6a71a1c8220b8d98f80a1dc02cffeb82", "ca4945f83b4e7706d1a270d58be35352cdb0e3f735298d9bbac169cd16a10a25", "cb8dfe65f3b49aefb036d7afcb2cfea6a22955f30159e607d05f87a3066fa8de", "d54d7b3afb1246b54bc583c38033613dce471dca4383cb2c2fd7a3c9ee59d163", "de139455ac77033c35863cd7862d564ae4d8dea60dc8debc3c5472dfb7db63c5"]}, {"name": "multifile-edit [panel] [typescript] - fs provider: move function from one file to another", "requests": ["0181c21b33be692fe8774a758f7e1129220b04b04465a9e2e3e3c49754040081", "01aa5091984405a9f08744dd97852a84a9fefe4870f42d694b496d28a9e9ad80", "05004c3e0a9030402e3cb7c3b0306f35b76f471dc5a19d5cc8f18e4c0d4f846a", "0526ba21f71571ea985cee7d330bcaaf302c906cdc6193920278e3503d8f6089", "08220492fb8f043ec1d78d0b24a72d7d6f6ddd33b045193152191ad10d0e399a", "0b829e41dd78230efb7510c3ee4e124150a26c27f37f3bba800443dff55908ff", "111f8e6d2f9c7d66e772622e31a071a5b8f63642eadb1559a8c10c91c3b1b639", "150bf069b953dd6147e8a3e02113453a7ac2117663bbd62bda64b1bc5f8aa6d2", "1669d33bf44948f42fd58a3680cc86a5d9a525930001a6202c7602645add3a67", "18d289108fb3788553b2cc82f48dbaa0be72190129e46126fe030a0ad867ffd1", "19552ce23a53ced1e49eeb5e20be8fe32a4d348c48695613a3cb163b83f2748c", "2301aa9faa8005b2bcfb96d46f5946349eddc1ffa4bd7681b7a6eb74a6a959df", "3498d2a1e9d9ae85ab8406f60424c6871f23164a36dfa11df03729e263c4adca", "35bd769f79b15656e08b50df44f7dff65ac464e27fb282517a6a0835e78f11e1", "4b06087655bd726088939fbff719e7e60fb389180f0a50c92f1acdc8160ccabf", "4bc5c253183e82153fd80a0b82d31741d1bed5537b18accf491cdcdb61e21ca5", "4fffed7722e1fb023b7d3a16947835a1c22e0245990ca8be0d13d2b4dfff4805", "61427625cfd7aa2a8a9e8d9b03c807dc100dcd345ca25e6ed1807fb32ff9df38", "6960e45421aa502815e1127638ee4998b71ce74a416edbf0f833eb6953e9238f", "78caa3347111d9708823b08d8a3fb524545e6cdc1a3f33ce74f8577a84578686", "828ff8778b39ab0d7ce887ab1ef597e44dda641a495693ed917aeb165cd03bf0", "848fcf7b2a77fcb20c26c566b934221bd79dce6fff7735d05369d512c54018c1", "861ec56ac82c127114d9b08bc6858386daf445f27a767f41b60f68253c9d3898", "8aa7c582aa95fb1a2f3f07c298b51b54995a7ca3a11210532a294b12e6df091d", "99111297c6bbb4e574691015adabcab1246a4a47b74d849d47791e0d802d02bc", "ba9b79c76875e15b00f0fb29c4895be9eb02c0d4d315a2d13bac43837f48f7d9", "ba9f5ee3f8ba940e64895f75aba1be2b3767813dcec03e6ada94ddb454c3f471", "c21f5e59f73c7ca6267f23cea6710ed9b546b852689fc59234e05dde38035bc1", "c28c828d71b31ba7a0a4c184c36348d2fe6576fab757949341ac8e31c00923b0", "c379db9d74de9b48e122d53d91a4b35a354c4cfe9c1beffba067b4d1fa5fe071", "c8e6d06d6cf7c5a2d843235f3a79d9fba9ba9a0724bcf77b5454cd288416cf30", "ccaca233fe4ad8f7aead816e487f98ec8e178a16b324753967d7686b5c3b234c", "ce1fa4df5e7c194f82155d72e22ee12fd4b2147aa8a575e77c90c398e6e56206", "d36d28c8071bad40d164a2db561a772ea9938e1c251e14b150feec0001a41b4e", "d56272906fbbe2dc6e9042acec48d64eb2522230cdb6e6a2c33b78953e75d050", "dc07edddc118c03d1bbffdbb4c9f3c770b98018a19dd8291d7a46575ff7d9a6d", "e99b41ac6ff584375e850db4d8dd349cbb6d125b257dbe658dd7290468b971f6", "ef0e456cfdfc0582a08204780235041d54548c06621e5b9f7d0657c68bb821f0", "f6d109277b40f98e185bb273266f3926bc23f90bc4d0ef2b413b5d6bb97938b1", "f7ceafaaa3e3e16f200d72d474c027d991c9620dc4fdc96f70d8d2f10a92d18d"]}, {"name": "multifile-edit [panel] [typescript] - import new helper function", "requests": ["0070533f8e7c60afbb0e9ba8bec9a26c6a9ee52815226ab7c382e99da8a8bb0c", "31e3eb382323f40ea7bb1b1e359fe4be7712d59bc1ad0bc9bf3ee7eb852e7e71", "4f88fd513f9e533c77e7eed1b09220faaa1b54b74275d33e2ac3ff58bd2012dd", "5f5b3e84b7cebd8b82c53017fb43589911ac86efe5ae7b59dcc1c4b3b68b9a48", "8bd334364a1d685d2b713b0645656e0f9ec01ea206565daecb962f18116b32ba", "8e4583e5ab8371ffd9ba3c9484e354feb6aaddbc69e577bff4e9722528a1e8c6", "adbdc720c46d94d9fdab599914e8e720e84c96041a7eaa457ef968daa2633a68", "b444cd0932109a61d9fcb42c74330e5000fe8ea2b6bb8218f20e64d2d37fd489", "d6581a9cc02e8e7c70dc9097cda5b113053a7eb1de513dea96cdf507c4541395", "f5a64f25638dde26cf3c6b12631245515a21504465a7363caf6b125c97c0f3a8", "fa051869f2cdeb9ab98f3063479d6f832a22e3db331191a5f12791571b899857", "fe2f6df4a1e9b3ac46d41e5cebc8af28b58b86cf970787c32880e9a4442b7f98"]}, {"name": "multifile-edit [panel] [typescript] - issue #8098: extract function to unseen file", "requests": ["0407fa4ed75bd1d0cb0d0afcf61bc59bd2505e430481c0bde0b3984fdacb199e", "094642aa928bac104197239d7f62757c5e0d3ddcbf33a1940a31070d06fd27e5", "1480cd7c22669c86351534491c7ec6fff2fd8cbb0989a44d53bcdbab56020507", "24da53129f54a25b8fed05e32c3a77cc2149dfcd88109dfecaac2efb1a247fb9", "3570856bcea5e315a9c507ffadb3718a2f7668eac34dee710ee08cacd00e8cdf", "3f450562e4e12e879b8a7c84afb69ba7571f6ef2b45cf28c0b1c1eaa041f0043", "4a0de9636f9d4ffb07a8a2ae3228fa8b48cb6f3ed6b58db34c76a5e70fa056ff", "4a1766ce95b344d19d5988eab135dd5453fca7982fbb83ced73d0e48910e88eb", "5fb5dbefe84ec31e993c56b990a15d4f144b824e37913ee74b821230a710d225", "97bc59813d24034c183ba04624fcb1c42989736c79b3e5a39081168f6242d1c4", "9c15bceb47eed230c5368b650bfcfc452de471d197c85ab3f96439945d5b3931", "b2b03ea295eb2226302b5685b9337ea23019a46c47eaae95956c512bf9a23cd5", "ca3878a7774435cdc9045bc6f1a3cbb864535e22d886ea20059f7cb0ee1086ed", "e7f189e4d13413a1566c5fd53827b1664e3395608ed54d54f853058b67a7de1a", "f50feffc97897fcccc2fd3bc353ab9bfb9e10f78017189b7c602d6eca51512e3", "ff06763a1027c2b6ea29e665595789e00c276926b5d9635565f66d04513f9a0e"]}, {"name": "multifile-edit [panel] [typescript] - Issue #9647", "requests": ["101d7e3d77fe72a94da2d1b80e98b3481f712c9497fb8b9ce8dc7ca5e741fcfa", "bfb323220e26d9da07cb52fd719e3bfe10f9f81fd505f98a5d25a096e322252a"]}]