[{"name": "/tests-inline2 [inline] [cpp] - can create a new test file", "requests": ["3711c4d0a9a6ebc6a7f000d87a4e35586012e69fe2abeff2ae609b411b1fccbd", "6cb0ca7352942ea6a711a9448344672da96d9c9095dc81641b8e36da08dc27f1", "857f541a1947c3431a10478511165c07e9656067a1c01a555cb9fa94bcfa2a90", "b548df4ac2daf7c78a091eaf41de2b554e589f309db92abadce2fd8d55ab492e", "d04052941a769ef7b884c35f486941477a5c987c034f55fb6739412733af2c6b"]}, {"name": "/tests-inline2 [inline] [csharp] - creates new test file with some assertions and uses correct file name", "requests": ["fe54a3bd60894949ffdc6486a9b0682ce11798793ad3db5c45a79eda6a4bbb94"]}, {"name": "/tests-inline2 [inline] [typescript] - BidiMap test generation (inside file)", "requests": ["2735875d0b044144f01734a60d6586dcde7184497b790589b48c5f61cce1ccf4", "2afd0d85824ee47e0d1427d7262c224141b3d555d78908fe5d20a58ca6b41860", "4ba5d24c8591ae594d24bfc731099cd4bdc7aaef83e84fd3167b588457f83af0", "658aef2c59d3ba323eda792de6386a34375a63d631a18a0ae6d3c811536ea564", "68bf8c318a7e92d700d62a341f164bc328ff23efa0a31018f90376ad5d163e90", "6c7aab42cddd47e000ca9f1519f20c87c7c7e2f942a7fbf42bcc6fe8e70cd61b", "7acb1f99fad7f72ef7567fc20c6ba21c44a498f9704c1148b661319ec22f08b1", "b0265faeb6308a424ca8c9c1891d61396cb3674bff90fe12ae361ca583b8e42e", "b1904a08e7e6f1759e1b8ff3df7aab433e766055b5b712538004330dea9f231c", "b4581902a73f99b85f6c294d63422eeb74ac0b9099530f5fc53cb485ebaf8a98", "d53e834c9ee747a0ae62021e3615433880de8f6a1005b0ba8c1ae556058bdd9d"]}, {"name": "/tests-inline2 [inline] [typescript] - BidiMap test generation (inside test)", "requests": ["1e64d9a94b517b9980188e61529fda830ba2ef1294abd52ed884ae5b1c22e314", "20dd5a1328984dd4ee24cef1baaff6cdc26669d96341cbdd79d81184f31c32dc", "54f76cd5af0429410795c8d63406b0abe7f96e6c8d9fd9378b8768a70754aa8b", "ab52ebec1595f1e75b7bd2bcb4dae44dafa4c99e7d247366bca029845db2c85a", "b06843645a5951b0916b580b2e1c424bd51fd32bbc174cdf7eccde581d2d20b6", "c4508439a5e8fe0f04cb5607b1fc7decb93f1a9e093c105c0b9238989a1e08fe", "c9d20fdaa26bb9a611f668d7d76a866960d91f35dcfbb6ad99dbed4934b83b9b", "d3b02da68ac677af8fb213348d2b4ad4f20cd715de38a1c07dc019f91b6a2c8f", "e1065deae516ecbed61d549c15f846a1952762d3a7cd3dda8ccd8da6160ddd7a", "e3e93290d38dd7f5bd998c6203ce7a03708567e0e556a21bf3e8dbf4530704f8", "fe00ad52dbeb9737bc932bf78e0ef4c26ae6c5094ccd1e3aea91b1b928fb73dc"]}, {"name": "/tests-inline2 [inline] [typescript] - can add a test after an existing one", "requests": ["22e4d5246f0c138aef3ef7d605460d0d4f71b15c93f2da7d27c2ac6f3462794d", "26a1d05a16e88e10bfde577ce32688737ae259dd2e02fa4011b9a89caf2c5a75", "6717bdae1c8202f9f799451488ae1291fdd9c3747944ca5157c6002632e8bc9e", "86902fee834fa99644d6818fe36b139891cf9a4569116df80269113c9bc742c4", "be5426f57f03a2c9fe2a32b4e816a56178d0785dc0859c05a8dc2666d3061c15", "cb8fd76a4ef1e3eb2e04399734c926fd02511cb949a597a411d853acbcb03bbc", "d073cf2b0362e6264674cd26258934d5d6c124c0737df5c11f8dac2d48b81661", "e687482a3b1b851c68c739de27cdd5d86319fc276cf4d67e71f9db40ae7eb501"]}, {"name": "/tests-inline2 [inline] [typescript] - can add a test after an existing one with empty line", "requests": ["222813a7d0fc5843101c25808a9f923e07c7c8e7cd48142bbcd2b3a393c6fef3", "58a0e042e160b92f512c5d3e89bcaab195499e53ac32dfc188ad3140e0a327e6", "591d47c40ca507cadd64b4e9178e19d9f4484d70a7e1d643cbd84717a9a03338", "69b7943fb9c564bc702b276fa699c4aae3b6f5a665fb750e745fd2d2baccefbe", "70c0c56b8aa48d5b5a0a2f34c71a073d53ed02dbcb70d2f186d6476e4c68e776", "73a849435b4e57440235f6ded75af9911e2dc28cf034baad7c9a81f035e89df0", "b55fcd36b6ec190814b0296755058093f0e24e844d543c1757b6b840bf810b36"]}, {"name": "/tests-inline2 [inline] [typescript] - supports chat variables", "requests": ["de6602c7379fc7c2562a46bdd4cf51a6d2c99ccab218f35bc68279116f50df07"]}, {"name": "/tests-inline2 [inline] [typescript] - ts-new-test", "requests": ["09cdeae3f26af4b7b009de8f25466cabae6112918ad3c4345e5c44bb6400006c", "1304aabd47914fab5e1e70c19208a169f5aa0b04137f804c8b486f8d245057b5", "3b3b20686fb67f5073767508ebfcc170082047d478d1d86a2c6b993c6e2fac1f", "7644f1c0910d768353d401bf740962ba7f9e653b33b26a59d84557a22471aefa", "8a8ebb8e9ad0f7ad7fde8ea97c46c27ef42ab88285a157d5f080e6129963f18d", "8ea916e11012c408a57a94399b00e87661355f563d8886601602857b2b245119", "94e194b57a188584175acc0e5b373e8208ffecad581adf64c6ed43a63bf9a4a9", "960d1da2bba97f5d747023a3859b034dddfc392b3047a51ddd1d24ee09358635", "abdbce8b1a1678059afe5de7313181ab89589c95610a9a44a7ad13ba18d8b59f", "c86a09ffc932c726f5dfa98bdf58fdde81f83e7d729228432c8dbe3857f8bb8e", "cfb44459d012ae4eaf901a07300925846a23ade79b4ff0a163f73ba57638add0"]}]