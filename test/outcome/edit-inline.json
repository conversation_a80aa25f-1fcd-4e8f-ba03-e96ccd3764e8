[{"name": "edit [inline] [cpp] - edit for cpp", "requests": ["35b4de4a268dc428accfbb6ce479fe2a64a88f1cb2a7334721ce112fc1de3a22", "7a654c30397cf71031d1c1cab4befcf1372fd200443878aadc80915da5c15f29"]}, {"name": "edit [inline] [cpp] - edit for macro", "requests": ["286f03be1afab40d46cac3344b317e1116e4b4f6cdc708e92ec520c233930a85", "99339d6b69f23cba348be3dd100ad78dfae098ce0fb340d84441508c98ba4d6f"]}, {"name": "edit [inline] [csharp] - issue release#275: Inline Diff refinement causes massive duplication of code", "requests": ["08967810069db4386a9e4606b13e4838800f92cec464808791cb0652fafde52e", "2588968e5453727f5858d3a737b9f219692ed026b021de7da421533cb03968b0"]}, {"name": "edit [inline] [css] - issue #6469", "requests": ["33c8fc8ab98ba0db3b47b2a035ac4295192e91892ece047605f10bcbc3cbf433", "7b4f08b1b3f139bb27d60c7b457137567e62413bca9609b4aedec0c25d92d6cd"]}, {"name": "edit [inline] [html] - issue #6614", "requests": ["1f854ae8f2e47ff72b3bed1e7f86bd09255f987914973fd2b1ab84c9a5a787af", "907e1b280d83ca9d5f62d950c37e1517690721abff25a27ca43ece3a9a6b77cf"]}, {"name": "edit [inline] [javascript] - issue #2946: Inline chat markers don't work", "requests": ["3f6da342930670d626b5a6f4eee608ae467c2bdfe3fc98836555f7a62940c2b0", "a9e0060d8ca886e6d18cc56d4f8fb58b3c2169e116f20c95caf08ecdd6bc9f6a"]}, {"name": "edit [inline] [javascript] - issue #6329", "requests": ["7a993b5f7ea0f550367d1059f78bfce5dc8f896e8308ea38b74624160f5d1118", "f2f1140dc7ab75c09b55e1723565b91186d3a1292abb20b13385cc2281de9573"]}, {"name": "edit [inline] [javascript] - issue #6956", "requests": ["72734fd53c14a72941a90f64e566d741d0075d82357ccd62d106171083e48fa1", "e46fbd429019ff080f25380aa48c7df29f906c606e45cd1db20f8e6896efb3c3"]}, {"name": "edit [inline] [javascript] - Issue #7282", "requests": ["5110c130a97ac325ef0ea62710d42c44fc570f02068327ee0764d9c00f602d40", "b8842f718ccc20d8d82f0934be40fa1c52c7954ce28672be49e10e67d8300e67"]}, {"name": "edit [inline] [json] - Inline chat does not leak system prompt", "requests": ["8cbf44f1a684bd6da57c5073e345c624999626c97ba4cc2b2f6fa67ebe7bad7b", "ab34e6bcc43b33fec0595d53e2a453ad99ce30b8d5d9307f06f040ffed8a2f25"]}, {"name": "edit [inline] [markdown] - issue #5899: make this code more efficient inside markdown", "requests": ["50735cfbb49c2cb7472e02707654ba843f302ce86dc42885707fabe537e625b5", "55d542dadd315cc8ebb796c2bf59689deb10b9f5030073cead4347fb1f96fe9d"]}, {"name": "edit [inline] [markdown] - merge markdown sections", "requests": ["2af45cbe21e61bec25c5e2d0a8274222441d24b56feec6a1db12f41eb696f10a", "e204f3a7d0d771315498629bd5cacd6fa27eb5ba9bf360c201df5d0134c118f7"]}, {"name": "edit [inline] [python] - issue #1198: Multi-lingual queries throw off the inline response formatting", "requests": ["737b598e389f6af67c7d0468813fdccd1bc14d3b5fe2777a2bc8a8010972943d", "86ebc47ca653e5dca74dac63d4ab806318d5dc9e5702cc9e2803af016b246670"]}, {"name": "edit [inline] [typescript] - Context Outline: TypeScript between methods", "requests": ["3544d4d524b609c7d837deec2fa0c3e970dd0e1fbc24ab7f8631f2dee2aa5f7d", "5d9d49662a13923ab794959fe611638d1680e62722c3e1c967fc313bf9daa9a6"]}, {"name": "edit [inline] [typescript] - Context Outline: TypeScript in method", "requests": ["965185167de27d921a0c9120114efd6f887f50804a85de8021501b33eee9446f", "b949960cd5955e5ee77f3e49bb645f583562832fb9e877f01726a9c6da0069e2"]}, {"name": "edit [inline] [typescript] - convert ternary to if/else in short function", "requests": ["044b3bde293c2e9ba74ac0740fd64ef58853c331ea1d72af395a6aee210ae53b", "b7e638fbc65d363626a169e9991e0208fdcd1dd4f3b00499f010dd9dfed73faf"]}, {"name": "edit [inline] [typescript] - edit: add enum variant", "requests": ["46fec090bfe441baca5a24990f52165b10af72f88aac34651452f9b620f2d771", "e3ea2f2e09bc4a23d06eb1294173691fded3b3c064aa5b180fb5bd4eb672d32c"]}, {"name": "edit [inline] [typescript] - edit: add toString1", "requests": ["218e4a347baea8bc9120dfca633a313a8a4bcb9fb184269d7931c8e3c5bbd799", "fe2e9784e7ca2a99b79943c089310f47d18ec1625aa36bdaa0a24f3419603663"]}, {"name": "edit [inline] [typescript] - edit: add toString2", "requests": ["080c60c4ed0be1f4ba23af22e166ce46187166956f6a6264fc9c7d2acaa5eb51", "52b9bb062b2d415072905be171d14a77e0305f92f19d0678bc33343297c411db"]}, {"name": "edit [inline] [typescript] - edit: import assert", "requests": ["3637484778d6dbbd36735ebdd40c79811dd27129b29423ae09f510f96672eafb", "cb7e528efd9efffdd76728c9cc0a77bf0509f72706982591e26db99154a56947"]}, {"name": "edit [inline] [typescript] - edit: import assert 2", "requests": ["3a9703c05b4be27077b110c6b56d00f4bbcf13df52bf644030314f67ab33d58f", "de3c4b01b3311244edb804554375a9e968376ee28d300a61c50a593640ba4919"]}, {"name": "edit [inline] [typescript] - Inline chat touching code outside of my selection #2988", "requests": ["65b7a0df2bfef8866e15f11da44117fd6554b85e13722e3f8a5e10c7cba3127f", "77b205f895b32ebabb07eb90f44e07bc994c09198ed736dab88ed36a9ab170cb"]}, {"name": "edit [inline] [typescript] - Inline chat touching code outside of my selection #2988 with good selection", "requests": ["1fb77d3674ada2b93fe4bd06ed3c79907077202985effbd9261ffe59207061e4", "960f4993c90cf47fc685e9a4598f31966b034ffaef82b849c4e6f35bd91b80b6"]}, {"name": "edit [inline] [typescript] - issue #2431: Inline Chat follow-up tweak ends up in noop text-only answer", "requests": ["033b76119e2cafe5a2e9c9f09998dc558e3baaf5dcba97db90815ac3f3b8ebd7", "0717b71f46fcf6106842a80881877f149fda103c345cb0ccc75284f7b4970b0a", "0dcbdd5f0a57260a539ea1acc3623230631072dd21c6f1a6180b817c9ca07507", "10187e07a805bc21e13dee7b1db3cbae988ec2e160d95067c746e5dd3246fa38", "2db28f02aaa86a72153e6151b64f6b00427aa40eebb654723d59cc8c05f3f4a6", "395b25f99976eae34be40bfb1f7e2a1897d3db9f33c6709baaf4b6dba25b8483", "3f210c7279e325b181b84fad028d0ccf7cd9059a1a74e6fdb4da0d459bcdcde7", "5515d5eea6d4f97bb0c87dea8a2387d0f5a3c79718e1ac58de141b313b4d847a", "87a88ae51439c8e4d674cfa47a3ba2dfb3b6ad226f49d71762fbcda20d9235bb", "b5a31b8389943fe29e4138b5a241ca21611fcd37ef77edf35625b903363a681f", "b69384b14c75c16872652baabe8fb38380dca20c5693f6d4d3af45ec7d18cf8d", "c51c586444039fb450b2aa26f2ede6704608ea49818002adb5ea749baa87484b", "e1aea1e9f41fc85884b78bfb120c28ad880f3f4336f0395b5dc3f3edb9a64184", "e2295c0b1eab7aac8102665b9401f6281a8966e466fdc20df36e0c6709f97cfe", "e4dc5a0f90a49c0debce4b3e180f5582d2922c1ea9147099e3bec7d22752eea1", "ef5bf8b3e98080e872ba71c7ac186ce59b12c4e5789c776b80843f9e21095560", "f274eed644c649b110b0dc36035261e3db4791a22e34e982b906a0b2804b75d4", "f6351b2b1c43a877e406a0150af705b06cb26fa6f92c43f2eb4726bb945bce9e"]}, {"name": "edit [inline] [typescript] - issue #246: Add comment sends request to sidebar", "requests": ["1396653c368b99d3dba754a7c6d4453b70f68c1c290759149708e459ca211531", "c2451c541c785e45a6e3d1835dcc951b5ab6e6716cff2f4baaaf9eea576ab770", "dfe415ba9a8228e128066085f6d9922c09ac0f0e5a4aea268c7efbf9286d3997"]}, {"name": "edit [inline] [typescript] - issue #3257: Inline chat ends up duplicating code", "requests": ["588899561e26df048e6355f4555cd2b0ca75074b86180f8ce696432ac9b6ec0b", "dc96c76c7ee088525b76301f38a909a74f670a48c0d2cd098e229bb19c9e45c4"]}, {"name": "edit [inline] [typescript] - issue #3575: Inline Chat in function expands to delete whole file", "requests": ["11a07fbaba02dd09b9ef184768e950c9d4a02f086581fc2b5a540d955e2e8bbd", "c4c1ad73b37dfab3819e4122669511106cc50bb97bd3a76027622aed7a9e98bf"]}, {"name": "edit [inline] [typescript] - issue #3759: add type", "requests": ["d0ab5df2b8e38118718de3bff9f337fd090923f2a5910018775f23d74e3344e2", "e52824a8b21c857707895163bfde685ee775e5494e0a12020d9682ac75dd2ddd"]}, {"name": "edit [inline] [typescript] - issue #404: Add a cat to a comment", "requests": ["b3a27b0774cb458dd277080dd160264d7b5d0511121f432bc3f4cd796b39ac78", "ffc3e2b509c96bb28cf27a27adc720a55a1927b6a73899364489369062b06608"]}, {"name": "edit [inline] [typescript] - issue #405: \"make simpler\" query is surprising", "requests": ["0c98d18499a37e12c41ef4ccdcf3254693043ae1cf4a0cd4c6148388f5bb9262", "a87ad524982bb77be71450725cb5f253d728bcbe2741ccab44c51531d4dabc77"]}, {"name": "edit [inline] [typescript] - issue #4149: If ChatGPT makes the request, send only the first 20 episodes", "requests": ["35d818bb7f0e3012d9555a68a962ce52ca5105b6daa311b8eb07114c531256e8", "bf137e96d2e2177d8289d94228e3bd92c0edcef776d561c0734d4f6566704524"]}, {"name": "edit [inline] [typescript] - issue #4151: Rewrite the selection to use async/await", "requests": ["9c0c74723dfc636ead1336be1010d2d49423d643213fb8da34d7c1f2d73c5fca", "a984c01bdce97d1936fd40fae1aa6f0953ea22393bd01ab7e449a7fff7ba9670"]}, {"name": "edit [inline] [typescript] - issue #4302: Code doesn't come with backticks", "requests": ["382bf88d51e8749f50e65067529adf41762832af3423983401a89e0667c6e135", "94b10d95e6c1ed1c44e286a6d1748d68dfdb0d7bbf4f33e0895e031a4c7f579c"]}, {"name": "edit [inline] [typescript] - issue #5710: Code doesn't come with backticks", "requests": ["b78c0fc564f7b5b4962d4380ed24876586f3735bb905728609e7cb7e15abb1c5", "fe1ba6e6f234eeeb2654184e692b1c2c96eda55052eea778fc80196107f4531c"]}, {"name": "edit [inline] [typescript] - issue #5755: Inline edits go outside the selection", "requests": ["7d8e8bf2bba16fed006a6c956d00adc16a16849cfe56f60baf17396d5ab0a31a", "cc1db88c34facbda8545adcaa5fd0a5dcb8868c1d48f5b25c4a2ed0221168f37"]}, {"name": "edit [inline] [typescript] - issue #6059", "requests": ["007b2e6492d01e77da1e61052ee2b6adb177dcbdbf053e98874b0ee55de3844d", "f1686ba40876318589ec9706eba3468ad9052c41f54ff3f0560b0b5afafcaf22"]}, {"name": "edit [inline] [typescript] - issue #6276", "requests": ["0aa106a3d9b370e05aa8f591a36ebc937f1ebe03e6035a21506f6daed79a3140", "b4d1897fa6ec38a7c2f92f9cf3e062786cb3f382c17d3f7afba818f6935862d2"]}, {"name": "edit [inline] [typescript] - issue #6973", "requests": ["06287bddee92d4703c9313f3515a96d1f36faf71b07d7f91edade9ce9b0b2889", "520713c4277c5a2cb0fe4f5092cac74b06a72aa6d324c5c65dfb1ddcb2744d84"]}, {"name": "edit [inline] [typescript] - issue #7202", "requests": ["45a21b9c9b76057642a2e8485577dfd43e14978b11dcffeaa54e132731ea4918", "8a01f0b5cbcb13b1ec587b8d6e1ec44fb521fa2b6640a47f2a642e2283ddbdf7"]}, {"name": "edit [inline] [typescript] - issue #7660", "requests": ["0cd64251b76845faa04f8f91782b30f4fee6948554a4bbfafad22201cdc345f1", "5a31fe135328b018013bab26c7607e39070dddc4a85deed746e3a2182611d5f4"]}, {"name": "edit [inline] [typescript] - Issue #7996 - use entire context window", "requests": ["38d592582f98a00f4ccd970ac7d6ab4e969d83dcd5772f7da5b1049cff389ca8", "87410b77f0fbec7fd133c2126876c51de7f836a97d04238e8758962a0a9775e3"]}, {"name": "edit [inline] [typescript] - Issue #8129 (no errors)", "requests": ["5eb79a369fc73fbf681bbc0ebe083aa006552968b964023aee42e3984bcf92b2", "a1e370ba849c4e180818192a829fc086caa4b99c396fe79cb4c55989d7031b81"]}, {"name": "edit [inline] [typescript] - Issue #8129 (no syntax errors)", "requests": ["5eb79a369fc73fbf681bbc0ebe083aa006552968b964023aee42e3984bcf92b2", "a1e370ba849c4e180818192a829fc086caa4b99c396fe79cb4c55989d7031b81"]}, {"name": "edit [inline] [typescript] - refactor for<PERSON>, but only selected one", "requests": ["97d37385f134767ea7c686288a0eb348e598aa3585dad3e7d99c6f7348da5890", "a1a4f37ddf99622d10c5819cb96ad2974e25e969b0b6cb2dacaab4a5a4cc7a98"]}, {"name": "edit [inline] [typescriptreact] - issue #7487", "requests": ["81cb11caf0640079722d6e690d9af6358ed636ba4f4c0310bba68b2b87370c17", "f5dab15bd2059f60dd433c5043e39fbb203d8684e0163a265f0a0d0d17b09ac7"]}]