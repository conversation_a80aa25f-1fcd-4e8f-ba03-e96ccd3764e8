[{"name": "/review [inline] [javascript] - Binary search with correct stop condition - (gpt-4.1-2025-04-14)", "requests": ["21b5680ee815d8845c644f66d2f0d757e953c3143960d38dce254c425de1bde8"]}, {"name": "/review [inline] [javascript] - Binary search with incorrect stop condition - (gpt-4.1-2025-04-14)", "requests": ["a93caf4850e1e44a3b49609e970ae0f7bf18ad190259adc720e5956b81e145fb"]}, {"name": "/review [inline] [python] - Bank account with lock acquisition - (gpt-4.1-2025-04-14)", "requests": ["2ed59079ba9a8b03561d95d189fb6990f23a5e122a83ddf287467696450b5056"]}, {"name": "/review [inline] [python] - Bank account with missing lock acquisition - (gpt-4.1-2025-04-14)", "requests": ["a4db5d94014a003a39bc63b71ad39de13d5dc42e5cc93df9b81776569a5d3d5f"]}, {"name": "/review [inline] [typescript] - InstantiationService this scoping bug - (gpt-4.1-2025-04-14)", "requests": ["d418391af60da4bb08c5bc865ad38e3708fe64e8319d31c2cd3c9ac8a2318977"]}, {"name": "/review [inline] [typescript] - InstantiationService this scoping fixed - (gpt-4.1-2025-04-14)", "requests": ["31221274b01b187f98011e71c5ebadae47054227fb5ab78a46f796908a0d3aea"]}]