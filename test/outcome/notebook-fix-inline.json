[{"name": "notebook (fix) [inline] [python] - (AML-10-29) instance of bool has no to_string member", "requests": ["6119da22c3d53ac3e50529600b470e969c49c32e38e14727a8e4d5cba4c78780"]}, {"name": "notebook (fix) [inline] [python] - (AML-10-35) can not access member", "requests": ["7ed2736c22a9b280015ee9a6757698d559cc9c5899ad177894641e54b5b1d49e"]}, {"name": "notebook (fix) [inline] [python] - (AML-10-36) can not be assigned 2", "requests": ["f1fa3ebaa3abfed951a1e9a0613c5569a1be1b5c52f4eb3f31f30e004b9f8d60"]}, {"name": "notebook (fix) [inline] [python] - (AML-10-4) parameter already assigned", "requests": ["2859c35ff38bb3a94be3438207a37e85828aab2eefdf681112cedeb391b13f72"]}, {"name": "notebook (fix) [inline] [python] - (AML-10-48) can not be assigned 3", "requests": ["75ea68a45ccf835a0d771b983dfd02c4aa1fb1e948eb30dbef276dda48b00ecf"]}, {"name": "notebook (fix) [inline] [python] - (AML-10-58) not defined", "requests": ["0796199345dff94962b0a9c1553fc505bcd7c5d0e18e31289c47d468fbd04855"]}, {"name": "notebook (fix) [inline] [python] - (AML-8-110) not defined", "requests": ["c3ffa1ee2aef24e9d483c5024da50dfb7f44938f1782faf945ebfaba092aaa56"]}, {"name": "notebook (fix) [inline] [python] - (AML-8-73) no value for argument in function call", "requests": ["8a8c955f238f4235560ada1e43ba91a5bc68e082ef4330ace4d576fb6a10bf6d"]}, {"name": "notebook (fix) [inline] [python] - all Annotated types should include at least two type arguments", "requests": ["a8bec3b49de718e97192faa46e9d6f3694a3efd2b2226066d3a2922e30a8fcca"]}, {"name": "notebook (fix) [inline] [python] - async cannot be used in a non-async function", "requests": ["21a9cf42c50bc35a5b0ef02eda98e4f3c10b724e29c4f6580868347e2d55595b"]}, {"name": "notebook (fix) [inline] [python] - await cannot be used in a non-async function", "requests": ["079b1b79306af62c7f0199a2534732241d72736a0859375380fa43791a73e45e"]}, {"name": "notebook (fix) [inline] [python] - bad token", "requests": ["aaef462c05402fd0e74d85b734ad771110930d5fe2289bf8655ed0615b898be2"]}, {"name": "notebook (fix) [inline] [python] - Bar does not define a do_something2 method", "requests": ["b5711fc6c8ee11ccc549763ec2a670429395ac32aea3da91c55160eb0c72bec9"]}, {"name": "notebook (fix) [inline] [python] - cannot instantiate abstract class", "requests": ["a3f5b9d4f481b224faa79c145969d44474a5a505c729963bf4d6c66d80f9e9aa"]}, {"name": "notebook (fix) [inline] [python] - general type issue", "requests": ["a8f4b8a6f66fec2e7f869b4efa3adf69fe83dcaa65ec76cfb2b18c37df6989fc"]}, {"name": "notebook (fix) [inline] [python] - optional member access", "requests": ["e320186f64fecbb3bc80e3819494c7f7b72dcd96414c540543f3c0d6e2524ef3"]}, {"name": "notebook (fix) [inline] [python] - should not generate an error for variables declared in outer scopes", "requests": ["0e2edf5d3117c7896a86272c4699c3ee7837577213754cbf436383d9a0967394"]}, {"name": "notebook (fix) [inline] [python] - unbound variable", "requests": ["3e6bea746acad96f860c75e44df781273f80c74b971eb62cf8af618a8267d06f"]}, {"name": "notebook (fix) [inline] [python] - undefined variable", "requests": ["af9a91d25f4d764f817add02ebf87bc6b77569eea46debd0c51437b375e6277b"]}]