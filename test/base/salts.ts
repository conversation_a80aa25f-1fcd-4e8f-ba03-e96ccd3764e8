/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

export const TestingCacheSalts = {
	requestCacheSalt: '2025-02-19T14:49:06.023Z',
	nesFetchCacheSalt: '2025-02-19T14:49:06.023Z',
	embeddingsCacheSalt: '2025-02-19T14:49:06.023Z',
	tscCacheSalt: '2025-02-19T14:49:06.023Z',
	roslynCacheSalt: '2025-03-13T17:34:05.873Z',
	eslintCacheSalt: '2025-02-19T14:49:06.023Z',
	pylintCacheSalt: '2025-02-19T14:49:06.023Z',
	pyrightCacheSalt: '2025-02-19T14:49:06.023Z',
	pythonCacheSalt: '2025-02-19T14:49:06.023Z',
	globalStateSalt: '2025-02-19T14:49:06.023Z',
	globalStorageSalt: 'NOT USED',
	notebookCacheSalt: '2025-02-19T14:49:06.023Z',
	docSearchCacheSalt: '2025-02-19T14:49:06.023Z',
	codeSearchCacheSalt: 'NOT USED',
	cppCacheSalt: '2025-02-19T14:49:06.023Z',
	chunksEndpointCacheSalt: '2025-02-19T14:49:06.023Z',
	ruffCacheSalt: '2025-02-19T14:49:06.023Z',
	globalStateCacheSalt: '2025-02-19T14:49:06.023Z',
	modelMetadata: '2025-07-17T07:56:24.816Z'
};
