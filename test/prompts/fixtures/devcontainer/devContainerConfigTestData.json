[{"template": "ghcr.io/devcontainers/templates/python", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "app.py", "example.env", "notebooks", "requirements.txt"], "repository": "microsoft/az-oai-chatgpt-streamlit-harness"}, {"template": "ghcr.io/devcontainers/templates/python", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", "CODE_OF_CONDUCT.md", "LICENSE.md", "README.md", "SECURITY.md", "app.py", "images"], "repository": "microsoft/Mastering-GitHub-Copilot-for-Paired-Programming"}, {"template": "ghcr.io/devcontainers/templates/dotnet", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/dotnet", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".editorconfig", ".gitattributes", ".github", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "CONTRIBUTING.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "TypeChat.sln", "examples", "nuget.config", "src", "tests"], "repository": "microsoft/typechat.net"}, {"template": "ghcr.io/devcontainers/templates/typescript-node", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".github", ".giti<PERSON>re", ".prettier<PERSON>", ".vscode", "CODE_OF_CONDUCT.md", "Gem<PERSON>le", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "_config.yml", "assets", "blocks.ts", "botsim", "calibration.ts", "constants.ts", "drivers", "i2c.ts", "icon.png", "messages.ts", "microbit.ts", "mkc-calliopemini.json", "mkc-dfrobotmaqueen.json", "mkc-dfrobotmaqueenplusv2.json", "mkc-elecfreakscutebot.json", "mkc-elecfreakscutebotpro.json", "mkc-inksmithk8.json", "mkc-keystudiominismartrobot.json", "mkc-kittenbotminilfr.json", "mkc-kittenbotnanobit.json", "mkc-kittenbotrobotbit.json", "mkc-yahboomtinybit.json", "mkc.json", "mkhex.sh", "package-lock.json", "package.json", "protocol", "pxt.json", "radio.ts", "remotes", "robotdriver.ts", "robots", "sim.ts", "storage.cpp", "storage.ts", "test.ts", "tester.ts", "tsconfig.json", "tutorials"], "repository": "microsoft/microbit-robot"}, {"template": "ghcr.io/devcontainers/templates/java", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/github-cli", "ghcr.io/devcontainers/features/java", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/powershell"], "files": [".devcontainer", ".github", ".giti<PERSON>re", ".vscode", "CHANGELOG.md", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "build.gradle", "components", "gradle.properties", "gradle", "gradlew", "gradlew.bat", "scripts", "settings.gradle", "spotless.groovy"], "repository": "microsoft/kiota-java"}, {"template": "ghcr.io/devcontainers/templates/typescript-node", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/github-cli", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/rust"], "files": [".devcontainer", ".gitattributes", ".github", ".giti<PERSON>re", ".prettieri<PERSON>re", ".prettierrc.js", ".vscode", "LICENSE", "README.md", "SECURITY.md", "apps", "build-tests-samples", "build-tests", "common", "eslint", "heft-plugins", "libraries", "repo-scripts", "rigs", "rush-plugins", "rush.json", "vscode-extensions", "webpack"], "repository": "microsoft/rushstack"}, {"template": "ghcr.io/devcontainers/templates/rust", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/rust"], "files": [".devcontainer", ".gitattributes", ".giti<PERSON>re", ".vscode", "CODE_OF_CONDUCT.md", "Cargo.lock", "Cargo.toml", "LICENSE", "README.md", "SECURITY.md", "src"], "repository": "microsoft/vscode-remote-try-rust"}, {"template": "ghcr.io/devcontainers/templates/typescript-node", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".azure-pipelines", ".devcontainer", ".gitattributes", ".github", ".giti<PERSON>re", ".husky", ".mocharc.json", ".nvmrc", ".prettieri<PERSON>re", ".prettier<PERSON>", ".vscode", "CHANGELOG.md", "CONTRIBUTING.md", "LICENSE.txt", "MAINTAINING.md", "README.md", "SECURITY.md", "ThirdPartyNotices.txt", "build", "docs", "editor.code-workspace", "gulpfile.js", "package-lock.json", "package.json", "samples", "scripts", "src", "test", "webpack-plugin", "website"], "repository": "microsoft/monaco-editor"}, {"template": "ghcr.io/devcontainers/templates/ruby", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/docker-in-docker", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/ruby"], "files": [".devcontainer", ".dockerignore", ".env", ".env.development", ".env.production", ".env.test", ".gitattributes", ".github", ".giti<PERSON>re", ".rspec", ".ruby-version", ".tool-versions", "CHANGELOG.md", "CODE_OF_CONDUCT.md", "Dockerfile", "Gem<PERSON>le", "Gemfile.lock", "LICENSE", "Procfile", "README.md", "Rakefile", "SECURITY.md", "SUPPORT.md", "aca-inferno.yaml", "aca-redis.yaml", "aca-worker.yaml", "config.ru", "config", "data", "docker-compose.background.yml", "docker-compose.yml", "lib", "run.sh", "setup.sh", "spec", "us_core_test_kit.gemspec", "worker.rb"], "repository": "microsoft/ahds-inferno"}, {"template": "ghcr.io/devcontainers/templates/dotnet", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/dotnet", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".github", ".giti<PERSON>re", ".vscode", ".vsts-ci.yml", ".vsts-pr.yml", ".vsts-release.yml", "Build.props", "CONTRIBUTING.md", "CredentialProvider.Microsoft.Tests", "CredentialProvider.Microsoft.VSIX", "CredentialProvider.Microsoft", "Directory.Build.props", "Directory.Build.targets", "Directory.Packages.props", "LICENSE", "MicrosoftCredentialProvider.sln", "README.md", "SECURITY.md", "build", "helpers", "nuget.config", "samples", "src", "test.bat"], "repository": "microsoft/artifacts-credprovider"}, {"template": "ghcr.io/devcontainers/templates/python", "features": ["ghcr.io/devcontainers/features/python"], "files": ["app.py"]}, {"template": "ghcr.io/devcontainers/templates/javascript-node", "features": ["ghcr.io/devcontainers/features/azure-cli", "ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/github-cli", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".github", ".giti<PERSON>re", ".git<PERSON><PERSON><PERSON>", ".prettier<PERSON>", ".vscode", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "dev.mjs", "devicescript", "infra", "local.env", "package.json", "power-connector", "sandbox.config.json", "src", "tsconfig.json", "yarn.lock"], "repository": "microsoft/devicescript-gateway"}, {"template": "ghcr.io/devcontainers/templates/python", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python", "ghcr.io/devcontainers/features/rust"], "files": [".ado", ".cargo", ".devcontainer", ".eslintrc.cjs", ".gitattributes", ".github", ".giti<PERSON>re", ".prettieri<PERSON>re", ".prettier<PERSON>", ".vscode", "CODE_OF_CONDUCT.md", "CONTRIBUTING.md", "Cargo.lock", "Cargo.toml", "LICENSE.txt", "README.md", "SECURITY.md", "SUPPORT.md", "build.py", "clippy.toml", "compiler", "docker", "fuzz", "jupyterlab", "katas", "language_service", "library", "makeNpmDrop.mjs", "npm", "package-lock.json", "package.json", "pip", "playground", "prereqs.py", "resource_estimator", "samples", "version.py", "vscode", "wasm", "widgets"], "repository": "microsoft/qsharp"}, {"template": "ghcr.io/devcontainers/templates/typescript-node", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".es<PERSON><PERSON><PERSON>", ".eslintrc.json", ".github", ".giti<PERSON>re", ".prettier<PERSON>", ".vscode", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "docker", "docs", "jest.config.json", "package-lock.json", "package.json", "rest_calls", "src", "tsconfig.json"], "repository": "microsoft/Commercial-Marketplace-SaaS-API-Emulator"}, {"template": "ghcr.io/devcontainers/templates/dotnet", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".giti<PERSON>re", ".vscode", "CODE_OF_CONDUCT.md", "LICENSE", "Program.cs", "README.md", "SECURITY.md", "appsettings.Development.json", "appsettings.HttpsDevelopment.json", "appsettings.json", "vscode-remote-try-dotnet.csproj"], "repository": "microsoft/vscode-remote-try-dotnet"}, {"template": "ghcr.io/devcontainers/templates/typescript-node", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".eslintrc.json", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "package-lock.json", "package.json", "src", "tsconfig.json"], "repository": "microsoft/vscode-file-downloader-api"}, {"template": "ghcr.io/devcontainers/templates/python", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", "CODE_OF_CONDUCT.md", "CONTRIBUTING.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "main.py", "status.txt"], "repository": "microsoft/crea-un-dataset"}, {"template": "ghcr.io/devcontainers/templates/dotnet", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", ".editorconfig", ".gitattributes", ".github", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "CONTRIBUTING.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "sk-csharp-azure-functions", "sk-csharp-chatgpt-plugin", "sk-csharp-console-chat", "sk-csharp-hello-world", "sk-java-hello-world", "sk-python-azure-functions-chatgpt-plugin", "sk-python-azure-functions", "sk-python-flask-chatgpt-plugin", "sk-python-hello-world", "sk-starters.sln", "sk-typescript-console-chat", "sk_csharp_apim_demo"], "repository": "microsoft/semantic-kernel-starters"}, {"template": "ghcr.io/devcontainers/templates/python", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", "AzBatchMDF2AnalyticsFormat.py", "AzureBatch.py", "CreateSampleMDF.py", "DecodeCSV.py", "DecodeParquet.py", "DecodeUtils.py", "Dockerfile", "MDF2AnalyticsFormat.py", "MDF2AnalyticsFormatProcessing.py", "MetadataTools.py", "README.md", "demo.kql", "deployment.kql", "requirements.txt"], "repository": "microsoft/adx-automotive-demos"}, {"template": "ghcr.io/devcontainers/templates/python", "features": ["ghcr.io/devcontainers/features/azure-cli", "ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "data", "exercises", "lectures", "qna-chat-with-langchain", "qna-quickstart-template", "qna-quickstart-with-gpt-index", "requirements.txt"], "repository": "microsoft/azure-openai-in-a-day-workshop"}, {"template": "ghcr.io/devcontainers/templates/dotnet", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/docker-in-docker", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/java", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", ".github", ".giti<PERSON>re", "CODEOWNERS", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "challenges", "completesolution", "exercisefiles"], "repository": "microsoft/CopilotHackathon"}, {"template": "ghcr.io/devcontainers/templates/python", "features": ["ghcr.io/devcontainers/features/azure-cli", "ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/nvidia-cuda", "ghcr.io/devcontainers/features/python"], "files": [".amlignore", ".devcontainer", ".gitattributes", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "Transparency_FAQ.md", "asr", "configs", "css", "diarization", "inference_pipeline", "requirements.txt", "run_inference.py", "run_training_css_local.py", "sample_data", "utils"], "repository": "microsoft/NOTSOFAR1-Challenge"}, {"template": "ghcr.io/devcontainers/templates/dotnet", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python", "ghcr.io/devcontainers/features/terraform"], "files": [".devcontainer", ".github", ".giti<PERSON>re", ".vscode", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "docs", "src", "terraform"], "repository": "microsoft/hands-on-lab-serverless"}, {"template": "ghcr.io/devcontainers/templates/python", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", ".giti<PERSON>re", "README.md", "examples", "notebooks", "package-lock.json", "package.json", "pyproject.toml", "pyrightconfig.json", "src", "tests"], "repository": "microsoft/TypeChat"}, {"template": "ghcr.io/devcontainers/templates/python", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", ".github", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "flushdb.py", "main.py", "models.py", "requirements.txt"], "repository": "microsoft/python-sample-vscode-fastapi-tutorial"}, {"template": "ghcr.io/devcontainers/templates/javascript-node", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".giti<PERSON>re", ".vscode", "README.md", "lerna.json", "package-lock.json", "package.json", "packages"], "repository": "microsoft/SharePoint-Embedded-Samples"}, {"template": "ghcr.io/devcontainers/templates/typescript-node", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".es<PERSON><PERSON><PERSON>", ".eslintrc.json", ".github", ".giti<PERSON>re", ".vscode", ".vscodeign<PERSON>", "CHANGELOG.md", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "development.md", "package-lock.json", "package.json", "src", "tsconfig.json", "webpack.config.js"], "repository": "microsoft/vscode-file-downloader"}, {"template": "ghcr.io/devcontainers/templates/go", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/go", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".gitattributes", ".giti<PERSON>re", ".vscode", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "go.mod", "hello", "server.go"], "repository": "microsoft/vscode-remote-try-go"}, {"template": "ghcr.io/devcontainers/templates/dotnet", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".github", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "Fast-Pass-Architecture-resized.png", "FastPass", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "deploy", "solutions"], "repository": "microsoft/fast-pass"}, {"template": "ghcr.io/devcontainers/templates/php", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".gitattributes", ".giti<PERSON>re", ".vscode", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "index.php"], "repository": "microsoft/vscode-remote-try-php"}, {"template": "ghcr.io/devcontainers/templates/dotnet", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "InterestingLinks.md", "LICENSE", "<PERSON><PERSON><PERSON>", "ProvisionAzureOpenAI.md", "README.md", "SECURITY.md", "SUPPORT.md", "SemanticKernel", "Vocabulary.md", "images", "quickstarts", "tutorials"], "repository": "microsoft/globalopenaihack"}, {"template": "ghcr.io/devcontainers/templates/typescript-node", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".github", ".giti<PERSON>re", ".prettierrc.cjs", ".vscode", "CHANGELOG.md", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "RELEASE.md", "SECURITY.md", "SUPPORT.md", "admin", "nx.json", "package.json", "packages", "website", "yarn.lock"], "repository": "microsoft/docusaurus-plugins"}, {"template": "ghcr.io/devcontainers/templates/ubuntu", "features": ["ghcr.io/devcontainers/features/azure-cli", "ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git"], "files": [".devcontainer", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "create-resource-group", "modifying-file-permissions", "redirection-and-pipelines", "viewing-files", "what-are-case-statements", "what-are-conditional-statements", "what-are-functions", "what-are-loops", "what-is-a-bash-script", "what-is-a-variable"], "repository": "microsoft/bash-for-beginners"}, {"template": "ghcr.io/devcontainers/templates/ubuntu", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/dotnet", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".giti<PERSON>re", ".vscode", "LICENSE", "MinimalApi", "dotnetconf-2023-student-zone-minimalapi-spa.sln", "frontend"], "repository": "microsoft/dotnetconf-studentzone"}, {"template": "ghcr.io/devcontainers/templates/ubuntu", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", ".vscode", "README.md", "devcontainer-template.json"], "repository": "microsoft/datascience-py-r"}, {"template": "ghcr.io/devcontainers/templates/ubuntu", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "azure-pipelines.yaml", "evals", "input.txt", "main.py", "requirements.txt", "summarizer.py"], "repository": "microsoft/promptflow-local-cicd-sample"}, {"template": "ghcr.io/devcontainers/templates/ubuntu", "features": ["ghcr.io/devcontainers/features/azure-cli", "ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/dotnet", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node", "ghcr.io/devcontainers/features/python"], "files": [".devcontainer", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "architecture.png", "azure.yaml", "infra", "screenshot.png", "scripts", "webapi", "webui"], "repository": "microsoft/azure-chat-with-your-photos-demo"}, {"template": "ghcr.io/devcontainers/templates/ubuntu", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".clang-format", ".devcontainer", ".github", ".giti<PERSON>re", ".git<PERSON><PERSON><PERSON>", "CMakeLists.txt", "CODE_OF_CONDUCT.md", "LICENSE", "<PERSON><PERSON><PERSON>", "README.md", "SECURITY.md", "SUPPORT.md", "boards", "devicescript", "pico-sdk", "sample-Makefile.user", "src"], "repository": "microsoft/devicescript-pico"}, {"template": "ghcr.io/devcontainers/templates/ubuntu", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/java"], "files": [".devcontainer", ".github", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "CONTRIBUTING.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "cli", "docs", "rest-server", "terraform"], "repository": "microsoft/NubesGen"}, {"template": "ghcr.io/devcontainers/templates/ubuntu", "features": ["ghcr.io/devcontainers/features/azure-cli", "ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/docker-from-docker", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/github-cli", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".editorconfig", ".giti<PERSON>re", "CODE_OF_CONDUCT.md", "DETAILS.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "certs", "dev.json", "hotd.json", "package-lock.json", "package.json", "qotd.json", "src", "vss-extension.json"], "repository": "microsoft/azdo-extension-team-randomizer"}, {"template": "ghcr.io/devcontainers/templates/ubuntu", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/docker-in-docker", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/github-cli", "ghcr.io/devcontainers/features/go", "ghcr.io/devcontainers/features/python", "ghcr.io/devcontainers/features/sshd"], "files": [".devcontainer", ".github", ".giti<PERSON>re", ".prettier<PERSON>", ".vscode", "CODE_OF_CONDUCT.md", "LICENSE", "README.md", "SECURITY.md", "SUPPORT.md", "docs", "env", "examples", "infra", "policies", "requirements.txt", "setup.sh"], "repository": "microsoft/confidential-aci-examples"}, {"template": "ghcr.io/devcontainers/templates/ubuntu", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/git"], "files": [".devcontainer", ".gitattributes", ".github", ".giti<PERSON>re", ".markdownlint.jsonc", ".openpublishing.publish.config.json", ".openpublishing.redirection.json", "LICENSE", "LICENSE-CODE", "README.md", "SECURITY.md", "ThirdPartyNotices.md", "includes", "vcpkg"], "repository": "microsoft/vcpkg-docs"}, {"template": "ghcr.io/devcontainers/templates/ubuntu", "features": ["ghcr.io/devcontainers/features/common-utils", "ghcr.io/devcontainers/features/dotnet", "ghcr.io/devcontainers/features/git", "ghcr.io/devcontainers/features/node"], "files": [".devcontainer", ".giti<PERSON>re", ".vscode", "LICENSE", "MinimalApi", "dotnetconf-2023-student-zone-minimalapi-spa.sln", "frontend"], "repository": "microsoft/dotnetconf-studentzone"}]